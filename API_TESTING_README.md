# GoKeys API Testing Guide

Hướng dẫn test API cho GoKeys License Management Platform theo thứ tự ràng buộc: Organization → Product → Policy → License → Machine.

## Cấu trúc Files

```
├── test_api.sh                    # Script test toàn diện theo thứ tự ràng buộc
├── test_individual_endpoints.sh   # Script test từng endpoint riêng lẻ
├── test_public_endpoints.sh       # Script test các endpoint public
├── Makefile.test                  # Makefile cho testing
└── API_TESTING_README.md          # File hướng dẫn này
```

## Cách sử dụng

### 1. Chu<PERSON>n bị

```bash
# Build ứng dụng
make -f Makefile.test build

# Chạy server
make -f Makefile.test run

# Kiểm tra trạng thái server
make -f Makefile.test status
```

### 2. Chạy Tests

#### Test toàn diện (Recommended)
```bash
# Chạy test theo thứ tự ràng buộc đầy đủ
make -f Makefile.test test

# Hoặc chạy trực tiếp
./test_api.sh
```

#### Test từng endpoint riêng lẻ
```bash
# Test các endpoint cơ bản
make -f Makefile.test test-individual

# Hoặc chạy trực tiếp
./test_individual_endpoints.sh
```

#### Test các endpoint public
```bash
# Test các endpoint không cần authentication
make -f Makefile.test test-public

# Hoặc chạy trực tiếp
./test_public_endpoints.sh
```

### 3. Theo dõi logs
```bash
# Xem logs server
make -f Makefile.test logs

# Hoặc
tail -f server.log
```

### 4. Dọn dẹp
```bash
# Dừng server
make -f Makefile.test stop

# Xóa build artifacts
make -f Makefile.test clean
```

## Thứ tự ràng buộc API

### 1. Organization (Tổ chức)
- **POST** `/api/v1/organizations` - Tạo tổ chức
- **GET** `/api/v1/organizations` - Liệt kê tổ chức
- **GET** `/api/v1/organizations/:id` - Lấy thông tin tổ chức
- **PUT** `/api/v1/organizations/:id` - Cập nhật tổ chức
- **DELETE** `/api/v1/organizations/:id` - Xóa tổ chức

### 2. Product (Sản phẩm)
- **POST** `/api/v1/products` - Tạo sản phẩm (cần organizationId)
- **GET** `/api/v1/products` - Liệt kê sản phẩm
- **GET** `/api/v1/products/:id` - Lấy thông tin sản phẩm
- **PUT** `/api/v1/products/:id` - Cập nhật sản phẩm
- **DELETE** `/api/v1/products/:id` - Xóa sản phẩm

### 3. Policy (Chính sách)
- **POST** `/api/v1/policies` - Tạo chính sách (cần productId)
- **GET** `/api/v1/policies` - Liệt kê chính sách
- **GET** `/api/v1/policies/:id` - Lấy thông tin chính sách
- **PUT** `/api/v1/policies/:id` - Cập nhật chính sách
- **DELETE** `/api/v1/policies/:id` - Xóa chính sách

### 4. License (Giấy phép)
- **POST** `/api/v1/licenses` - Tạo license (cần policyId, organizationId)
- **GET** `/api/v1/licenses` - Liệt kê license
- **GET** `/api/v1/licenses/:id` - Lấy thông tin license
- **PUT** `/api/v1/licenses/:id` - Cập nhật license
- **DELETE** `/api/v1/licenses/:id` - Xóa license
- **POST** `/api/v1/licenses/:id/actions/validate` - Validate license (Public)
- **POST** `/api/v1/licenses/:id/actions/suspend` - Tạm dừng license
- **POST** `/api/v1/licenses/:id/actions/reinstate` - Khôi phục license

### 5. Machine (Máy)
- **POST** `/api/v1/machines` - Tạo machine (cần licenseId)
- **GET** `/api/v1/machines` - Liệt kê machine
- **GET** `/api/v1/machines/:id` - Lấy thông tin machine
- **PUT** `/api/v1/machines/:id` - Cập nhật machine
- **DELETE** `/api/v1/machines/:id` - Xóa machine
- **POST** `/api/v1/machines/:id/actions/heartbeat` - Machine heartbeat (Public)

### 6. User (Người dùng)
- **POST** `/api/v1/users` - Tạo user
- **GET** `/api/v1/users` - Liệt kê user
- **GET** `/api/v1/users/:id` - Lấy thông tin user
- **PUT** `/api/v1/users/:id` - Cập nhật user
- **DELETE** `/api/v1/users/:id` - Xóa user

## Endpoints Public (Không cần Authentication)

- **GET** `/health` - Health check
- **GET** `/` - Root endpoint
- **POST** `/api/v1/licenses/:id/actions/validate` - Validate license
- **POST** `/api/v1/machines/:id/actions/heartbeat` - Machine heartbeat

## Ví dụ Test Manual

### Tạo Organization
```bash
curl -X POST http://localhost:8080/api/v1/organizations \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Organization",
    "description": "A test organization",
    "website": "https://test.com",
    "email": "<EMAIL>"
  }'
```

### Tạo Product (cần Organization ID)
```bash
curl -X POST http://localhost:8080/api/v1/products \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "YOUR_ORG_ID",
    "name": "Test Product",
    "description": "A test product",
    "version": "1.0.0"
  }'
```

### Validate License (Public endpoint)
```bash
curl -X POST http://localhost:8080/api/v1/licenses/YOUR_LICENSE_ID/actions/validate
```

## Troubleshooting

### Server không start được
```bash
# Kiểm tra port 8080 có bị chiếm không
lsof -i :8080

# Kiểm tra logs
cat server.log
```

### Test script không chạy được
```bash
# Cấp quyền execute
chmod +x test_api.sh
chmod +x test_individual_endpoints.sh
chmod +x test_public_endpoints.sh

# Kiểm tra curl có sẵn không
which curl

# Kiểm tra jq có sẵn không (optional, để format JSON)
which jq
```

### API trả về lỗi
- Kiểm tra server có đang chạy không: `make -f Makefile.test status`
- Kiểm tra logs: `make -f Makefile.test logs`
- Kiểm tra thứ tự tạo resources (Organization → Product → Policy → License → Machine)

## Notes

- Tất cả API endpoints hiện tại không yêu cầu authentication (đã được comment out tạm thời)
- Script test sẽ tự động tạo và xóa resources theo đúng thứ tự
- Sử dụng `jq` để format JSON output đẹp hơn (optional)
- Server mặc định chạy trên port 8080
