# Authentication Guide - GoKeys License Manager

## Overview

GoKeys License Manager supports dual authentication system:
- **JWT Tokens** for web UI and browser-based applications
- **API Tokens** for programmatic access, CLI tools, and integrations

Both token types use the same `Authorization: Bearer <token>` header format.

## Token Types

### JWT Tokens (Web UI)
- **Purpose**: Web browser authentication, session management
- **Format**: Standard JWT (no prefix)
- **Lifetime**: 15 minutes (access) + 7 days (refresh)
- **Content**: Minimal user info, no permissions
- **Example**: `eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...`

### API Tokens (Programmatic Access)
- **Purpose**: API access, CLI tools, integrations
- **Format**: Prefixed tokens for easy identification
- **Lifetime**: Configurable (default: no expiration)
- **Content**: Granular permission scopes
- **Prefixes**:
  - `ldr_token_pat_` - Personal Access Token
  - `ldr_token_org_` - Organization Token
  - `ldr_token_svc_` - Service Token
  - `ldr_token_int_` - Integration Token
  - `ldr_token_` - Generic Token

## Authentication Flow

### Web UI Authentication (JWT)

```bash
# 1. Login to get JWT token pair
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password"
}

# Response
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 900
}

# 2. Use access token for API calls
GET /api/licenses
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...

# 3. Refresh when expired
POST /api/auth/refresh
Authorization: Bearer <refresh_token>
```

### API Token Authentication

```bash
# 1. Create API token
POST /api/tokens
Authorization: Bearer <jwt_access_token>
{
  "name": "CI/CD Token",
  "type": "service",
  "scopes": [
    {
      "scope": "org:123e4567-e89b-12d3-a456-426614174000",
      "resource_type": "license",
      "actions": ["read", "validate"],
      "attributes": []
    }
  ]
}

# Response
{
  "token": "ldr_token_svc_a1b2c3d4e5f6...",
  "name": "CI/CD Token",
  "expires_at": null
}

# 2. Use API token for programmatic access
GET /api/licenses
Authorization: Bearer ldr_token_svc_a1b2c3d4e5f6...
```

## Token Identification

The authentication middleware automatically detects token type:

```go
// JWT Token (no prefix)
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...

// API Token (with prefix)
Authorization: Bearer ldr_token_pat_a1b2c3d4e5f6...
```

## Permission System

### JWT Tokens
- **No permissions stored** in token
- **Permissions loaded** from database on each request
- **Always up-to-date** permissions
- **Minimal token size** (~300-400 bytes)

### API Tokens
- **Scoped permissions** defined at creation
- **Granular control** over resources and actions
- **No database lookup** needed for permission checks
- **Larger token size** but cached validation

## Security Features

### JWT Tokens
- **Short-lived access tokens** (15 minutes)
- **Refresh token rotation** for extended sessions
- **Session binding** for additional security
- **IP and User-Agent tracking**

### API Tokens
- **Configurable expiration**
- **Usage tracking** (last used IP/time)
- **Revocation capability**
- **Scope-based access control**

## Usage Examples

### Web Application
```javascript
// Login and store tokens
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password })
});

const { access_token, refresh_token } = await response.json();
localStorage.setItem('access_token', access_token);
localStorage.setItem('refresh_token', refresh_token);

// Make authenticated requests
const apiResponse = await fetch('/api/licenses', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
  }
});
```

### CLI Tool
```bash
# Set API token as environment variable
export LDR_TOKEN="ldr_token_pat_a1b2c3d4e5f6..."

# Use in CLI commands
curl -H "Authorization: Bearer $LDR_TOKEN" \
     https://api.example.com/licenses
```

### Integration Service
```python
import requests

# API token for service-to-service communication
API_TOKEN = "ldr_token_svc_a1b2c3d4e5f6..."

headers = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json"
}

response = requests.get(
    "https://api.example.com/licenses",
    headers=headers
)
```

## Error Handling

### Authentication Errors
```json
{
  "error": {
    "code": "INVALID_JWT",
    "message": "Invalid JWT token"
  }
}
```

### Authorization Errors
```json
{
  "error": {
    "code": "INSUFFICIENT_PERMISSIONS",
    "message": "Insufficient permissions for this operation"
  }
}
```

## Best Practices

### JWT Tokens
1. **Store securely** in httpOnly cookies or secure storage
2. **Implement refresh logic** before token expiration
3. **Clear tokens** on logout
4. **Use HTTPS** for all token transmission

### API Tokens
1. **Use specific scopes** - grant minimal required permissions
2. **Set expiration dates** for temporary access
3. **Rotate tokens regularly** for long-term use
4. **Store securely** - treat as passwords
5. **Monitor usage** - track last used timestamps
6. **Revoke unused tokens** - clean up old tokens

### General Security
1. **Use HTTPS only** - never transmit tokens over HTTP
2. **Validate on server** - never trust client-side validation
3. **Log authentication events** - monitor for suspicious activity
4. **Implement rate limiting** - prevent brute force attacks
5. **Regular security audits** - review token usage and permissions

## Migration from Legacy System

### Existing API Keys
Legacy API keys with `X-API-Key` header are still supported:
```bash
# Legacy format (still works)
GET /api/licenses
X-API-Key: old_api_key_format

# New format (recommended)
GET /api/licenses
Authorization: Bearer ldr_token_pat_new_format
```

### Gradual Migration
1. **Create new API tokens** with `ldr_token_` prefix
2. **Update applications** to use `Authorization: Bearer` header
3. **Deprecate old tokens** after migration period
4. **Remove legacy support** in future version
