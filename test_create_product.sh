#!/bin/bash

# Test the fixed create product API
curl -X 'POST' \
  'http://localhost:8080/api/v1/organizations/99999999-9999-9999-9999-999999999999/products' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "Enterprise Software",
  "distribution_strategy": "licensed",
  "platforms": [
    "linux",
    "windows",
    "macos"
  ],
  "url": "https://enterprise.com/software",
  "metadata": {
    "additionalProp1": {}
  }
}'
