﻿# Ruby Code Check
- User wants me to check Ruby code and remember this request for future reference.

# Go Implementation
- User wants me to skip Legacy crypto schemes since Go implementation is new, not legacy.
- Go implementation should always use latest techniques and logic, removing old/legacy code for cleanliness.
- User wants me to remember this mapping approach for future reference - Go implementation uses clean constant names without version suffixes, mapping to latest underlying values.
- User wants Go implementation to rename account to organization and remove group and environment fields entirely for simplicity.