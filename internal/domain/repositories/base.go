package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseRepository defines common repository operations
type BaseRepository[T any] interface {
	Create(ctx context.Context, entity *T) error
	GetByID(ctx context.Context, id uuid.UUID) (*T, error)
	Update(ctx context.Context, entity *T) error
	Delete(ctx context.Context, id uuid.UUID) error
	SoftDelete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, filter ListFilter) ([]*T, int64, error)
	Count(ctx context.Context, filter ListFilter) (int64, error)
	Exists(ctx context.Context, id uuid.UUID) (bool, error)
}

// ListFilter represents common filtering options
type ListFilter struct {
	Page           int
	PageSize       int
	SortBy         string
	SortOrder      string // ASC or DESC
	Search         string
	Filters        map[string]interface{}
	OrganizationID *uuid.UUID
	EnvironmentID  *uuid.UUID
	IncludeDeleted bool
}

// DefaultListFilter returns a default list filter
func DefaultListFilter() ListFilter {
	return ListFilter{
		Page:      1,
		PageSize:  20,
		SortBy:    "created_at",
		SortOrder: "DESC",
		Filters:   make(map[string]interface{}),
	}
}

// BaseRepositoryImpl provides base implementation for common operations
type BaseRepositoryImpl[T any] struct {
	db *gorm.DB
}

// NewBaseRepository creates a new base repository
func NewBaseRepository[T any](db *gorm.DB) *BaseRepositoryImpl[T] {
	return &BaseRepositoryImpl[T]{
		db: db,
	}
}

// Create creates a new entity
func (r *BaseRepositoryImpl[T]) Create(ctx context.Context, entity *T) error {
	// Debug: Check database connection health
	if sqlDB, err := r.db.DB(); err == nil {
		if pingErr := sqlDB.Ping(); pingErr != nil {
			// Log detailed connection info
			stats := sqlDB.Stats()
			return fmt.Errorf("database connection failed - OpenConnections: %d, InUse: %d, Idle: %d, WaitCount: %d, error: %w",
				stats.OpenConnections, stats.InUse, stats.Idle, stats.WaitCount, pingErr)
		}
	} else {
		return fmt.Errorf("failed to get sql.DB: %w", err)
	}

	return r.db.WithContext(ctx).Create(entity).Error
}

// GetByID retrieves an entity by ID
func (r *BaseRepositoryImpl[T]) GetByID(ctx context.Context, id uuid.UUID) (*T, error) {
	var entity T
	err := r.db.WithContext(ctx).First(&entity, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

// Update updates an entity
func (r *BaseRepositoryImpl[T]) Update(ctx context.Context, entity *T) error {
	return r.db.WithContext(ctx).Save(entity).Error
}

// Delete permanently deletes an entity
func (r *BaseRepositoryImpl[T]) Delete(ctx context.Context, id uuid.UUID) error {
	var entity T
	return r.db.WithContext(ctx).Unscoped().Delete(&entity, "id = ?", id).Error
}

// SoftDelete soft deletes an entity
func (r *BaseRepositoryImpl[T]) SoftDelete(ctx context.Context, id uuid.UUID) error {
	var entity T
	return r.db.WithContext(ctx).Delete(&entity, "id = ?", id).Error
}

// List retrieves entities with filtering and pagination
func (r *BaseRepositoryImpl[T]) List(ctx context.Context, filter ListFilter) ([]*T, int64, error) {
	var entities []*T
	var total int64

	query := r.buildQuery(ctx, filter)

	// Count total records
	if err := query.Model(new(T)).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (filter.Page - 1) * filter.PageSize
	query = query.Offset(offset).Limit(filter.PageSize)

	// Execute query
	if err := query.Find(&entities).Error; err != nil {
		return nil, 0, err
	}

	return entities, total, nil
}

// Count returns the total count of entities matching the filter
func (r *BaseRepositoryImpl[T]) Count(ctx context.Context, filter ListFilter) (int64, error) {
	var count int64
	query := r.buildQuery(ctx, filter)
	err := query.Model(new(T)).Count(&count).Error
	return count, err
}

// Exists checks if an entity exists by ID
func (r *BaseRepositoryImpl[T]) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(new(T)).Where("id = ?", id).Count(&count).Error
	return count > 0, err
}

// buildQuery builds a GORM query based on the filter
func (r *BaseRepositoryImpl[T]) buildQuery(ctx context.Context, filter ListFilter) *gorm.DB {
	query := r.db.WithContext(ctx)

	// Include soft deleted records if requested
	if filter.IncludeDeleted {
		query = query.Unscoped()
	}

	// Apply organization filter
	if filter.OrganizationID != nil {
		query = query.Where("organization_id = ?", *filter.OrganizationID)
	}

	// Apply environment filter
	if filter.EnvironmentID != nil {
		query = query.Where("environment_id = ?", *filter.EnvironmentID)
	}

	// Apply custom filters
	for key, value := range filter.Filters {
		query = query.Where(key, value)
	}

	// Apply search (this would need to be customized per entity type)
	if filter.Search != "" {
		// Generic search on name field if it exists
		query = query.Where("name ILIKE ?", "%"+filter.Search+"%")
	}

	// Apply sorting
	if filter.SortBy != "" {
		order := filter.SortBy
		if filter.SortOrder == "DESC" {
			order += " DESC"
		} else {
			order += " ASC"
		}
		query = query.Order(order)
	}

	return query
}

// Transaction wraps operations in a database transaction
func (r *BaseRepositoryImpl[T]) Transaction(ctx context.Context, fn func(*gorm.DB) error) error {
	return r.db.WithContext(ctx).Transaction(fn)
}

// GetDB returns the underlying database connection
func (r *BaseRepositoryImpl[T]) GetDB() *gorm.DB {
	return r.db
}

// Repository collection interfaces for Hexagonal Architecture

// OrganizationRepository defines organization-specific operations
type OrganizationRepository interface {
	BaseRepository[entities.Organization]
	GetBySlug(ctx context.Context, slug string) (*entities.Organization, error)
}

// ProductRepository defines product-specific operations
type ProductRepository interface {
	BaseRepository[entities.Product]
	GetByCode(ctx context.Context, code string, organizationID uuid.UUID) (*entities.Product, error)
	GetByOrganizationID(ctx context.Context, organizationID uuid.UUID) ([]*entities.Product, error)
}

// PolicyRepository defines policy-specific operations
type PolicyRepository interface {
	BaseRepository[entities.Policy]
	GetByProduct(ctx context.Context, productID uuid.UUID) ([]*entities.Policy, error)
	GetByScheme(ctx context.Context, scheme string, organizationID uuid.UUID) ([]*entities.Policy, error)
}

// LicenseRepository defines license-specific operations
type LicenseRepository interface {
	BaseRepository[entities.License]
	GetByKey(ctx context.Context, key string) (*entities.License, error)
	GetByPolicy(ctx context.Context, policyID uuid.UUID) ([]*entities.License, error)
	GetExpiring(ctx context.Context, organizationID uuid.UUID, beforeDate time.Time) ([]*entities.License, error)
	UpdateLastValidated(ctx context.Context, licenseID uuid.UUID) error
	IncrementValidationCount(ctx context.Context, licenseID uuid.UUID) error
}

// MachineRepository defines machine-specific operations
type MachineRepository interface {
	BaseRepository[entities.Machine]
	GetByFingerprint(ctx context.Context, fingerprint string, licenseID uuid.UUID) (*entities.Machine, error)
	GetByLicense(ctx context.Context, licenseID uuid.UUID) ([]*entities.Machine, error)
	UpdateHeartbeat(ctx context.Context, machineID uuid.UUID) error
	GetStale(ctx context.Context, olderThan time.Time) ([]*entities.Machine, error)
}

// UserRepository defines user-specific operations
type UserRepository interface {
	BaseRepository[entities.User]
	GetByEmail(ctx context.Context, email string) (*entities.User, error)
	UpdateLastLogin(ctx context.Context, userID uuid.UUID) error
}

// SessionRepository defines session-specific operations
type SessionRepository interface {
	BaseRepository[entities.Session]
	GetByTokenHash(ctx context.Context, tokenHash string) (*entities.Session, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.Session, error)
	CleanupExpired(ctx context.Context) (int64, error)
	UpdateLastUsed(ctx context.Context, sessionID uuid.UUID) error
}

// APITokenRepository defines API token-specific operations
type APITokenRepository interface {
	BaseRepository[entities.APIToken]
	GetByTokenHash(ctx context.Context, tokenHash string) (*entities.APIToken, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.APIToken, error)
	GetByOrganizationID(ctx context.Context, organizationID uuid.UUID) ([]*entities.APIToken, error)
	UpdateLastUsed(ctx context.Context, tokenID uuid.UUID) error
}

// UsersOrganizationRepository defines simple user-organization membership operations
type UsersOrganizationRepository interface {
	Create(ctx context.Context, userOrg *entities.UsersOrganization) error
	Delete(ctx context.Context, userID, orgID string) error
	GetUserOrganizations(ctx context.Context, userID string) ([]*entities.UsersOrganization, error)
	GetOrganizationUsers(ctx context.Context, orgID string) ([]*entities.UsersOrganization, error)
	IsUserInOrganization(ctx context.Context, userID, orgID string) (bool, error)
	GetByUserAndOrganization(ctx context.Context, userID, orgID string) (*entities.UsersOrganization, error)
}

// PermissionRepository defines resource-based permission operations
type PermissionRepository interface {
	Create(ctx context.Context, permission *entities.Permission) error
	GetByID(ctx context.Context, id string) (*entities.Permission, error)
	Update(ctx context.Context, permission *entities.Permission) error
	Delete(ctx context.Context, id string) error

	// Core permission operations
	GetUserPermissions(ctx context.Context, userID string) ([]*entities.Permission, error)
	GetPermissionsByScope(ctx context.Context, scopePattern string) ([]*entities.Permission, error)
	GetUsersWithPermission(ctx context.Context, scope, resourceType, action string) ([]*entities.Permission, error)

	// Permission management
	GrantPermission(ctx context.Context, userID uuid.UUID, scope, resourceType string, actions []string, grantedBy *uuid.UUID, expiresAt *time.Time) (*entities.Permission, error)
	RevokePermission(ctx context.Context, userID, scope, resourceType string) error
	RevokeUserPermissions(ctx context.Context, userID string) error

	// Expiration management
	GetExpiredPermissions(ctx context.Context) ([]*entities.Permission, error)
	CleanExpiredPermissions(ctx context.Context) (int64, error)

	// Quick checks
	HasPermission(ctx context.Context, userID, scope, resourceType, action string) (bool, error)
}

// UserOrganizationRepository defines user-organization relationship operations (DEPRECATED - use UsersOrganizationRepository)
// COMMENTED OUT: This interface references the deprecated entities.UserOrganization entity
// Use UsersOrganizationRepository instead for simple membership tracking
/*
type UserOrganizationRepository interface {
	BaseRepository[entities.UserOrganization]
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.UserOrganization, error)
	GetByOrganizationID(ctx context.Context, organizationID uuid.UUID) ([]*entities.UserOrganization, error)
	GetByUserAndOrganization(ctx context.Context, userID, organizationID uuid.UUID) (*entities.UserOrganization, error)
	DeleteByUserAndOrganization(ctx context.Context, userID, organizationID uuid.UUID) error

	// OPA integration methods
	GetActiveByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.UserOrganization, error)
	GetAllActiveForOPA(ctx context.Context) ([]*entities.UserOrganization, error)
	GetByResourceConstraint(ctx context.Context, resourceType string, resourceID *uuid.UUID) ([]*entities.UserOrganization, error)
	GetAdminsByOrganization(ctx context.Context, organizationID uuid.UUID) ([]*entities.UserOrganization, error)
	CreateConstraint(ctx context.Context, userOrg *entities.UserOrganization) error
}
*/
