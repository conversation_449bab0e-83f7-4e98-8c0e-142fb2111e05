package entities

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// APIToken represents API access tokens
type APIToken struct {
	ID             uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID         *uuid.UUID `json:"user_id,omitempty" gorm:"column:user_id;type:uuid"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty" gorm:"column:organization_id;type:uuid"`
	Name           string     `json:"name" gorm:"size:255;not null"`

	TokenHash   string   `json:"-" gorm:"column:token_hash;size:255;uniqueIndex;not null"`
	Permissions []string `json:"permissions" gorm:"type:text[]"` // Array of permissions for OPA

	Active     bool       `json:"active" gorm:"default:true"`
	ExpiresAt  *time.Time `json:"expires_at,omitempty" gorm:"column:expires_at"`
	LastUsedAt *time.Time `json:"last_used_at,omitempty" gorm:"column:last_used_at"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"column:created_at;not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"column:updated_at;not null"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deleted_at;index"`

	// Relations
	User         *User         `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Organization *Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
}

// TableName overrides the table name used by GORM
func (APIToken) TableName() string {
	return "api_tokens"
}

// IsExpired checks if the token has expired
func (t *APIToken) IsExpired() bool {
	if t.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*t.ExpiresAt)
}

// IsActive checks if the token is currently active and not expired
func (t *APIToken) IsActive() bool {
	return t.Active && !t.IsExpired()
}

// HasPermission checks if the token has a specific permission
func (t *APIToken) HasPermission(permission string) bool {
	for _, p := range t.Permissions {
		if p == permission {
			return true
		}
	}
	return false
}

// HasAnyPermission checks if the token has any of the given permissions
func (t *APIToken) HasAnyPermission(permissions ...string) bool {
	for _, permission := range permissions {
		if t.HasPermission(permission) {
			return true
		}
	}
	return false
}

// HasAllPermissions checks if the token has all of the given permissions
func (t *APIToken) HasAllPermissions(permissions ...string) bool {
	for _, permission := range permissions {
		if !t.HasPermission(permission) {
			return false
		}
	}
	return true
}

// UpdateLastUsed updates the last used timestamp
func (t *APIToken) UpdateLastUsed() {
	now := time.Now()
	t.LastUsedAt = &now
}
