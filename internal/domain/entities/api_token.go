package entities

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// APITokenScope represents the scope of permissions for an API token
type APITokenScope struct {
	Scope        string   `json:"scope"`         // "system", "org:uuid", "resource", "owner"
	ResourceType string   `json:"resource_type"` // "*", "product", "license", etc.
	Actions      []string `json:"actions"`       // ["*"] or ["create", "read", "update"]
	Attributes   []string `json:"attributes"`    // [] for all, or specific resource IDs
}

// APIToken represents API access tokens with permission-based authorization
type APIToken struct {
	ID             uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID         *uuid.UUID `json:"user_id,omitempty" gorm:"column:user_id;type:uuid"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty" gorm:"column:organization_id;type:uuid"`
	Name           string     `json:"name" gorm:"size:255;not null"`
	Description    string     `json:"description,omitempty" gorm:"size:500"`

	// Token authentication
	TokenHash   string `json:"-" gorm:"column:token_hash;size:255;uniqueIndex;not null"`
	TokenPrefix string `json:"token_prefix" gorm:"size:10;not null"` // First 8 chars for identification

	// Permission-based scopes (replaces simple permissions array)
	Scopes []APITokenScope `json:"scopes" gorm:"type:jsonb;not null;default:'[]'"`

	// Token lifecycle
	Active     bool       `json:"active" gorm:"default:true"`
	ExpiresAt  *time.Time `json:"expires_at,omitempty" gorm:"column:expires_at"`
	LastUsedAt *time.Time `json:"last_used_at,omitempty" gorm:"column:last_used_at"`
	LastUsedIP string     `json:"last_used_ip,omitempty" gorm:"size:45"` // IPv4/IPv6

	// Security metadata
	CreatedByIP string `json:"created_by_ip,omitempty" gorm:"size:45"`
	UserAgent   string `json:"user_agent,omitempty" gorm:"size:500"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"column:created_at;not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"column:updated_at;not null"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deleted_at;index"`

	// Relations
	User         *User         `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Organization *Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
}

// TableName overrides the table name used by GORM
func (APIToken) TableName() string {
	return "api_tokens"
}

// IsExpired checks if the token has expired
func (t *APIToken) IsExpired() bool {
	if t.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*t.ExpiresAt)
}

// IsActive checks if the token is currently active and not expired
func (t *APIToken) IsActive() bool {
	return t.Active && !t.IsExpired()
}

// HasScope checks if the token has a specific scope with action
func (t *APIToken) HasScope(scope, resourceType, action string) bool {
	for _, tokenScope := range t.Scopes {
		if t.scopeMatches(tokenScope, scope, resourceType, action) {
			return true
		}
	}
	return false
}

// HasScopeForResource checks if token can perform action on specific resource
func (t *APIToken) HasScopeForResource(resourceType, action, resourceID string) bool {
	for _, tokenScope := range t.Scopes {
		if t.scopeMatchesResource(tokenScope, resourceType, action, resourceID) {
			return true
		}
	}
	return false
}

// CanAccessOrganization checks if token can access a specific organization
func (t *APIToken) CanAccessOrganization(orgID string) bool {
	for _, scope := range t.Scopes {
		// System scope can access all organizations
		if scope.Scope == "system" {
			return true
		}
		// Organization scope must match
		if scope.Scope == fmt.Sprintf("org:%s", orgID) {
			return true
		}
	}
	return false
}

// GetPermissionScopes returns all permission scopes as Permission-compatible format
func (t *APIToken) GetPermissionScopes() []Permission {
	var permissions []Permission
	for _, scope := range t.Scopes {
		permissions = append(permissions, Permission{
			Scope:        scope.Scope,
			ResourceType: scope.ResourceType,
			Actions:      scope.Actions,
		})
	}
	return permissions
}

// UpdateLastUsed updates the last used timestamp and IP
func (t *APIToken) UpdateLastUsed(ip string) {
	now := time.Now()
	t.LastUsedAt = &now
	t.LastUsedIP = ip
}

// GenerateToken generates a new API token string
func (t *APIToken) GenerateToken() (string, error) {
	// Generate random bytes for token
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return "", fmt.Errorf("failed to generate random token: %w", err)
	}

	// Create token string with standard prefix
	// Permissions trong scopes đã chứa toàn bộ thông tin cần thiết
	prefix := "ldr_token_"

	tokenString := prefix + hex.EncodeToString(tokenBytes)

	// Store hash and prefix for identification
	hash := sha256.Sum256([]byte(tokenString))
	t.TokenHash = hex.EncodeToString(hash[:])
	t.TokenPrefix = tokenString[:12] // Longer prefix for better identification

	return tokenString, nil
}

// scopeMatches checks if a token scope matches the required permission
func (t *APIToken) scopeMatches(tokenScope APITokenScope, scope, resourceType, action string) bool {
	// Check scope match
	if !t.scopeStringMatches(tokenScope.Scope, scope) {
		return false
	}

	// Check resource type match
	if tokenScope.ResourceType != "*" && tokenScope.ResourceType != resourceType {
		return false
	}

	// Check action match
	return t.hasAction(tokenScope.Actions, action)
}

// scopeMatchesResource checks if scope matches for specific resource
func (t *APIToken) scopeMatchesResource(tokenScope APITokenScope, resourceType, action, resourceID string) bool {
	// Check resource type match
	if tokenScope.ResourceType != "*" && tokenScope.ResourceType != resourceType {
		return false
	}

	// Check action match
	if !t.hasAction(tokenScope.Actions, action) {
		return false
	}

	// Check resource ID match (if attributes specified)
	if len(tokenScope.Attributes) > 0 {
		found := false
		for _, attr := range tokenScope.Attributes {
			if attr == resourceID {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}

// scopeStringMatches checks if scope strings match with hierarchy
func (t *APIToken) scopeStringMatches(tokenScope, requiredScope string) bool {
	// Exact match
	if tokenScope == requiredScope {
		return true
	}

	// System scope matches everything
	if tokenScope == "system" {
		return true
	}

	// Organization scope matches resource scope within same org
	if strings.HasPrefix(tokenScope, "org:") && strings.HasPrefix(requiredScope, "org:") {
		return tokenScope == requiredScope
	}

	return false
}

// hasAction checks if action is allowed
func (t *APIToken) hasAction(allowedActions []string, action string) bool {
	for _, allowedAction := range allowedActions {
		if allowedAction == "*" || allowedAction == action {
			return true
		}
	}
	return false
}
