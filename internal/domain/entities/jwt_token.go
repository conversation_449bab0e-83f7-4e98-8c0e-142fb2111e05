package entities

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// JWTTokenType represents the type of JWT token
type JWTTokenType string

const (
	JWTTokenTypeAccess  JWTTokenType = "access"  // Short-lived access tokens
	JWTTokenTypeRefresh JWTTokenType = "refresh" // Long-lived refresh tokens
)

// JWTClaims represents the claims structure for JWT tokens
// Chỉ chứa thông tin cơ bản, permissions sẽ được load từ DB khi cần
type JWTClaims struct {
	// Standard JWT claims
	Issuer    string    `json:"iss"`           // Issuer
	Subject   string    `json:"sub"`           // Subject (user ID)
	Audience  []string  `json:"aud,omitempty"` // Audience
	ExpiresAt time.Time `json:"exp"`           // Expiration time
	NotBefore time.Time `json:"nbf"`           // Not before
	IssuedAt  time.Time `json:"iat"`           // Issued at
	JWTID     string    `json:"jti"`           // JWT ID

	// Custom claims for our application - chỉ thông tin cơ bản
	UserID        string                 `json:"user_id"`
	SessionID     string                 `json:"session_id,omitempty"`
	TokenType     JWTTokenType           `json:"token_type"`
	Organizations []JWTOrganizationClaim `json:"organizations,omitempty"` // Chỉ cho access token

	// Security metadata
	IPAddress string `json:"ip_address,omitempty"`
	UserAgent string `json:"user_agent,omitempty"`
}

// JWTPermissionClaim represents a permission in JWT format
type JWTPermissionClaim struct {
	Scope        string   `json:"scope"`         // "system", "org:uuid", "resource", "owner"
	ResourceType string   `json:"resource_type"` // "*", "product", "license", etc.
	Actions      []string `json:"actions"`       // ["*"] or ["create", "read", "update"]
	Attributes   []string `json:"attributes"`    // [] for all, or specific resource IDs
}

// JWTOrganizationClaim represents organization membership in JWT
type JWTOrganizationClaim struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Slug string `json:"slug"`
	Role string `json:"role,omitempty"` // User's role in this organization
}

// JWTTokenBuilder helps build JWT tokens with proper claims
type JWTTokenBuilder struct {
	user          *User
	session       *Session
	tokenType     JWTTokenType
	expiresIn     time.Duration
	organizations []UsersOrganization
}

// NewJWTTokenBuilder creates a new JWT token builder
func NewJWTTokenBuilder(user *User) *JWTTokenBuilder {
	return &JWTTokenBuilder{
		user:      user,
		tokenType: JWTTokenTypeAccess,
		expiresIn: 15 * time.Minute, // Default 15 minutes for access tokens
	}
}

// WithSession adds session information to the token
func (b *JWTTokenBuilder) WithSession(session *Session) *JWTTokenBuilder {
	b.session = session
	return b
}

// WithTokenType sets the token type
func (b *JWTTokenBuilder) WithTokenType(tokenType JWTTokenType) *JWTTokenBuilder {
	b.tokenType = tokenType
	if tokenType == JWTTokenTypeRefresh {
		b.expiresIn = 7 * 24 * time.Hour // 7 days for refresh tokens
	}
	return b
}

// WithExpiresIn sets the expiration duration
func (b *JWTTokenBuilder) WithExpiresIn(duration time.Duration) *JWTTokenBuilder {
	b.expiresIn = duration
	return b
}

// WithOrganizations adds organization memberships to the token
func (b *JWTTokenBuilder) WithOrganizations(organizations []UsersOrganization) *JWTTokenBuilder {
	b.organizations = organizations
	return b
}

// Build creates the JWT claims structure
func (b *JWTTokenBuilder) Build() *JWTClaims {
	now := time.Now()

	claims := &JWTClaims{
		Issuer:    "gokeys",
		Subject:   b.user.ID.String(),
		ExpiresAt: now.Add(b.expiresIn),
		NotBefore: now,
		IssuedAt:  now,
		JWTID:     uuid.New().String(),

		UserID:    b.user.ID.String(),
		TokenType: b.tokenType,
	}

	// Add session ID if available
	if b.session != nil {
		claims.SessionID = b.session.ID.String()
	}

	// Convert organizations to JWT format (chỉ cho access token)
	if b.tokenType == JWTTokenTypeAccess && len(b.organizations) > 0 {
		claims.Organizations = make([]JWTOrganizationClaim, len(b.organizations))
		for i, org := range b.organizations {
			claims.Organizations[i] = JWTOrganizationClaim{
				ID:   org.OrganizationID.String(),
				Name: "", // Sẽ được load từ DB khi cần
				Slug: "", // Sẽ được load từ DB khi cần
			}
		}
	}

	return claims
}

// CanAccessOrganization checks if JWT can access a specific organization
// Chỉ check membership, permissions sẽ được check từ database
func (c *JWTClaims) CanAccessOrganization(orgID string) bool {
	// Check if user is member of the organization
	for _, org := range c.Organizations {
		if org.ID == orgID {
			return true
		}
	}
	return false
}

// IsExpired checks if the JWT token has expired
func (c *JWTClaims) IsExpired() bool {
	return time.Now().After(c.ExpiresAt)
}

// IsValid checks if the JWT token is currently valid
func (c *JWTClaims) IsValid() bool {
	now := time.Now()
	return now.After(c.NotBefore) && now.Before(c.ExpiresAt)
}

// ToJSON converts claims to JSON string
func (c *JWTClaims) ToJSON() (string, error) {
	data, err := json.Marshal(c)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON creates claims from JSON string
func FromJSON(jsonStr string) (*JWTClaims, error) {
	var claims JWTClaims
	err := json.Unmarshal([]byte(jsonStr), &claims)
	if err != nil {
		return nil, err
	}
	return &claims, nil
}
