package entities

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// JWTTokenType represents the type of JWT token
type JWTTokenType string

const (
	JWTTokenTypeAccess  JWTTokenType = "access"  // Short-lived access tokens
	JWTTokenTypeRefresh JWTTokenType = "refresh" // Long-lived refresh tokens
)

// JWTClaims represents the claims structure for JWT tokens
type JWTClaims struct {
	// Standard JWT claims
	Issuer    string    `json:"iss"`           // Issuer
	Subject   string    `json:"sub"`           // Subject (user ID)
	Audience  []string  `json:"aud,omitempty"` // Audience
	ExpiresAt time.Time `json:"exp"`           // Expiration time
	NotBefore time.Time `json:"nbf"`           // Not before
	IssuedAt  time.Time `json:"iat"`           // Issued at
	JWTID     string    `json:"jti"`           // JWT ID

	// Custom claims for our application
	UserID         string                 `json:"user_id"`
	SessionID      string                 `json:"session_id,omitempty"`
	OrganizationID string                 `json:"organization_id,omitempty"`
	TokenType      JWTTokenType           `json:"token_type"`
	Permissions    []JWTPermissionClaim   `json:"permissions"`
	Organizations  []JWTOrganizationClaim `json:"organizations"`
	
	// Security metadata
	IPAddress string `json:"ip_address,omitempty"`
	UserAgent string `json:"user_agent,omitempty"`
}

// JWTPermissionClaim represents a permission in JWT format
type JWTPermissionClaim struct {
	Scope        string   `json:"scope"`         // "system", "org:uuid", "resource", "owner"
	ResourceType string   `json:"resource_type"` // "*", "product", "license", etc.
	Actions      []string `json:"actions"`       // ["*"] or ["create", "read", "update"]
	Attributes   []string `json:"attributes"`    // [] for all, or specific resource IDs
}

// JWTOrganizationClaim represents organization membership in JWT
type JWTOrganizationClaim struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Slug string `json:"slug"`
	Role string `json:"role,omitempty"` // User's role in this organization
}

// JWTTokenBuilder helps build JWT tokens with proper claims
type JWTTokenBuilder struct {
	user         *User
	session      *Session
	tokenType    JWTTokenType
	expiresIn    time.Duration
	permissions  []Permission
	organizations []UsersOrganization
}

// NewJWTTokenBuilder creates a new JWT token builder
func NewJWTTokenBuilder(user *User) *JWTTokenBuilder {
	return &JWTTokenBuilder{
		user:      user,
		tokenType: JWTTokenTypeAccess,
		expiresIn: 15 * time.Minute, // Default 15 minutes for access tokens
	}
}

// WithSession adds session information to the token
func (b *JWTTokenBuilder) WithSession(session *Session) *JWTTokenBuilder {
	b.session = session
	return b
}

// WithTokenType sets the token type
func (b *JWTTokenBuilder) WithTokenType(tokenType JWTTokenType) *JWTTokenBuilder {
	b.tokenType = tokenType
	if tokenType == JWTTokenTypeRefresh {
		b.expiresIn = 7 * 24 * time.Hour // 7 days for refresh tokens
	}
	return b
}

// WithExpiresIn sets the expiration duration
func (b *JWTTokenBuilder) WithExpiresIn(duration time.Duration) *JWTTokenBuilder {
	b.expiresIn = duration
	return b
}

// WithPermissions adds permissions to the token
func (b *JWTTokenBuilder) WithPermissions(permissions []Permission) *JWTTokenBuilder {
	b.permissions = permissions
	return b
}

// WithOrganizations adds organization memberships to the token
func (b *JWTTokenBuilder) WithOrganizations(organizations []UsersOrganization) *JWTTokenBuilder {
	b.organizations = organizations
	return b
}

// Build creates the JWT claims structure
func (b *JWTTokenBuilder) Build() *JWTClaims {
	now := time.Now()
	
	claims := &JWTClaims{
		Issuer:    "gokeys",
		Subject:   b.user.ID.String(),
		ExpiresAt: now.Add(b.expiresIn),
		NotBefore: now,
		IssuedAt:  now,
		JWTID:     uuid.New().String(),
		
		UserID:    b.user.ID.String(),
		TokenType: b.tokenType,
	}
	
	// Add session ID if available
	if b.session != nil {
		claims.SessionID = b.session.ID.String()
	}
	
	// Convert permissions to JWT format
	claims.Permissions = make([]JWTPermissionClaim, len(b.permissions))
	for i, perm := range b.permissions {
		claims.Permissions[i] = JWTPermissionClaim{
			Scope:        perm.Scope,
			ResourceType: perm.ResourceType,
			Actions:      perm.Actions,
			Attributes:   []string{}, // TODO: Add attributes support
		}
	}
	
	// Convert organizations to JWT format
	claims.Organizations = make([]JWTOrganizationClaim, len(b.organizations))
	for i, org := range b.organizations {
		if org.Organization != nil {
			claims.Organizations[i] = JWTOrganizationClaim{
				ID:   org.Organization.ID.String(),
				Name: org.Organization.Name,
				Slug: org.Organization.Slug,
			}
		}
	}
	
	return claims
}

// HasPermission checks if JWT claims contain a specific permission
func (c *JWTClaims) HasPermission(scope, resourceType, action string) bool {
	for _, perm := range c.Permissions {
		if c.permissionMatches(perm, scope, resourceType, action) {
			return true
		}
	}
	return false
}

// HasPermissionForResource checks if JWT can perform action on specific resource
func (c *JWTClaims) HasPermissionForResource(resourceType, action, resourceID string) bool {
	for _, perm := range c.Permissions {
		if c.permissionMatchesResource(perm, resourceType, action, resourceID) {
			return true
		}
	}
	return false
}

// CanAccessOrganization checks if JWT can access a specific organization
func (c *JWTClaims) CanAccessOrganization(orgID string) bool {
	// Check if user is member of the organization
	for _, org := range c.Organizations {
		if org.ID == orgID {
			return true
		}
	}
	
	// Check if user has system-wide permissions
	for _, perm := range c.Permissions {
		if perm.Scope == "system" {
			return true
		}
		if perm.Scope == "org:"+orgID {
			return true
		}
	}
	
	return false
}

// IsExpired checks if the JWT token has expired
func (c *JWTClaims) IsExpired() bool {
	return time.Now().After(c.ExpiresAt)
}

// IsValid checks if the JWT token is currently valid
func (c *JWTClaims) IsValid() bool {
	now := time.Now()
	return now.After(c.NotBefore) && now.Before(c.ExpiresAt)
}

// ToJSON converts claims to JSON string
func (c *JWTClaims) ToJSON() (string, error) {
	data, err := json.Marshal(c)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON creates claims from JSON string
func FromJSON(jsonStr string) (*JWTClaims, error) {
	var claims JWTClaims
	err := json.Unmarshal([]byte(jsonStr), &claims)
	if err != nil {
		return nil, err
	}
	return &claims, nil
}

// Helper methods for permission matching

func (c *JWTClaims) permissionMatches(perm JWTPermissionClaim, scope, resourceType, action string) bool {
	// Check scope match with hierarchy
	if !c.scopeMatches(perm.Scope, scope) {
		return false
	}
	
	// Check resource type match
	if perm.ResourceType != "*" && perm.ResourceType != resourceType {
		return false
	}
	
	// Check action match
	return c.hasAction(perm.Actions, action)
}

func (c *JWTClaims) permissionMatchesResource(perm JWTPermissionClaim, resourceType, action, resourceID string) bool {
	// Check resource type match
	if perm.ResourceType != "*" && perm.ResourceType != resourceType {
		return false
	}
	
	// Check action match
	if !c.hasAction(perm.Actions, action) {
		return false
	}
	
	// Check resource ID match (if attributes specified)
	if len(perm.Attributes) > 0 {
		found := false
		for _, attr := range perm.Attributes {
			if attr == resourceID {
				found = true
				break
			}
		}
		return found
	}
	
	return true
}

func (c *JWTClaims) scopeMatches(permScope, requiredScope string) bool {
	// Exact match
	if permScope == requiredScope {
		return true
	}
	
	// System scope matches everything
	if permScope == "system" {
		return true
	}
	
	return false
}

func (c *JWTClaims) hasAction(allowedActions []string, action string) bool {
	for _, allowedAction := range allowedActions {
		if allowedAction == "*" || allowedAction == action {
			return true
		}
	}
	return false
}
