package entities

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// Permission represents optimized flattened permissions for high-performance lookups
type Permission struct {
	ID     uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID uuid.UUID `json:"user_id" gorm:"column:user_id;type:uuid;not null"`

	// Flattened permission key for O(1) lookups
	// Format: "{scope}:{resource_type}:{action}"
	// Examples: "system:*:*", "org:company-abc:product:read", "resource:license:validate"
	PermissionKey string `json:"permission_key" gorm:"column:permission_key;size:255;not null;uniqueIndex:idx_user_permission"`

	// Resource IDs for specific resource permissions
	// [] = wildcard (all resources), ["id1", "id2"] = specific resources
	ResourceIDs pq.StringArray `json:"resource_ids" gorm:"type:jsonb;default:'[]'"`

	// Metadata
	GrantedBy *uuid.UUID `json:"granted_by,omitempty" gorm:"column:granted_by;type:uuid"`
	GrantedAt time.Time  `json:"granted_at" gorm:"column:granted_at;not null;default:CURRENT_TIMESTAMP"`
	ExpiresAt *time.Time `json:"expires_at,omitempty" gorm:"column:expires_at"`

	// Relations
	User    User  `json:"user" gorm:"foreignKey:UserID"`
	Granter *User `json:"granter,omitempty" gorm:"foreignKey:GrantedBy"`
}

// Legacy Permission structure for backward compatibility
type LegacyPermission struct {
	ID           uuid.UUID      `json:"id"`
	UserID       uuid.UUID      `json:"user_id"`
	Scope        string         `json:"scope"`
	ResourceType string         `json:"resource_type"`
	Actions      pq.StringArray `json:"actions"`
	GrantedBy    *uuid.UUID     `json:"granted_by,omitempty"`
	GrantedAt    time.Time      `json:"granted_at"`
	ExpiresAt    *time.Time     `json:"expires_at,omitempty"`
}

// TableName overrides the table name used by GORM
func (Permission) TableName() string {
	return "permissions"
}

// Permission scopes
const (
	ScopeSystem = "system" // System-wide access
	ScopeOwner  = "owner"  // Owner-based access
)

// Common resource types
const (
	ResourceTypeAll          = "*"
	ResourceTypeOrganization = "organization"
	ResourceTypeProduct      = "product"
	ResourceTypePolicy       = "policy"
	ResourceTypeLicense      = "license"
	ResourceTypeMachine      = "machine"
	ResourceTypeUser         = "user"
	ResourceTypeAPIToken     = "api_token"
	ResourceTypeSession      = "session"
)

// Common actions
const (
	ActionAll      = "*"
	ActionCreate   = "create"
	ActionRead     = "read"
	ActionUpdate   = "update"
	ActionDelete   = "delete"
	ActionValidate = "validate"
	ActionCheckout = "checkout"
)

// PermissionKeyParts represents the parsed components of a permission key
type PermissionKeyParts struct {
	Scope        string
	ResourceType string
	Action       string
}

// ParsePermissionKey parses a flattened permission key into its components
func ParsePermissionKey(key string) (*PermissionKeyParts, error) {
	parts := strings.Split(key, ":")
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid permission key format: %s", key)
	}

	return &PermissionKeyParts{
		Scope:        parts[0],
		ResourceType: parts[1],
		Action:       parts[2],
	}, nil
}

// BuildPermissionKey creates a flattened permission key
func BuildPermissionKey(scope, resourceType, action string) string {
	return fmt.Sprintf("%s:%s:%s", scope, resourceType, action)
}

// GetScope extracts the scope from the permission key
func (p *Permission) GetScope() string {
	parts, err := ParsePermissionKey(p.PermissionKey)
	if err != nil {
		return ""
	}
	return parts.Scope
}

// GetResourceType extracts the resource type from the permission key
func (p *Permission) GetResourceType() string {
	parts, err := ParsePermissionKey(p.PermissionKey)
	if err != nil {
		return ""
	}
	return parts.ResourceType
}

// GetAction extracts the action from the permission key
func (p *Permission) GetAction() string {
	parts, err := ParsePermissionKey(p.PermissionKey)
	if err != nil {
		return ""
	}
	return parts.Action
}

// IsSystemScope checks if this is a system-wide permission
func (p *Permission) IsSystemScope() bool {
	return strings.HasPrefix(p.PermissionKey, "system:")
}

// IsOwnerScope checks if this is an owner-based permission
func (p *Permission) IsOwnerScope() bool {
	return strings.HasPrefix(p.PermissionKey, "owner:")
}

// IsOrgScope checks if this is an organization-scoped permission
func (p *Permission) IsOrgScope() bool {
	return strings.HasPrefix(p.PermissionKey, "org:")
}

// IsResourceScope checks if this is a specific resource permission
func (p *Permission) IsResourceScope() bool {
	return strings.HasPrefix(p.PermissionKey, "resource:")
}

// GetOrgID extracts organization ID from org scope
// Returns empty string if not an org scope
func (p *Permission) GetOrgID() string {
	if !p.IsOrgScope() {
		return ""
	}
	scope := p.GetScope()
	if len(scope) > 4 && scope[:4] == "org:" {
		return scope[4:] // Remove "org:" prefix
	}
	return ""
}

// GetResourceInfo extracts resource type and ID from resource scope
// Returns empty strings if not a resource scope
func (p *Permission) GetResourceInfo() (resourceType, resourceID string) {
	if !p.IsResourceScope() {
		return "", ""
	}

	// Resource type is in the permission key
	return p.GetResourceType(), ""
}

// HasAction checks if permission allows a specific action
func (p *Permission) HasAction(action string) bool {
	permAction := p.GetAction()
	return permAction == ActionAll || permAction == action
}

// MatchesResourceType checks if permission applies to a resource type
func (p *Permission) MatchesResourceType(resourceType string) bool {
	permResourceType := p.GetResourceType()
	return permResourceType == ResourceTypeAll || permResourceType == resourceType
}

// MatchesResource checks if permission applies to a specific resource
func (p *Permission) MatchesResource(resourceType, resourceID string) bool {
	// Check resource type first
	if !p.MatchesResourceType(resourceType) {
		return false
	}

	// If no specific resource IDs, it's a wildcard permission
	if len(p.ResourceIDs) == 0 {
		return true
	}

	// Check if resource ID is in the allowed list
	for _, allowedID := range p.ResourceIDs {
		if allowedID == resourceID {
			return true
		}
	}

	return false
}

// IsExpired checks if the permission has expired
func (p *Permission) IsExpired() bool {
	if p.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*p.ExpiresAt)
}

// IsActive checks if the permission is currently active (not expired)
func (p *Permission) IsActive() bool {
	return !p.IsExpired()
}

// Helper functions

// splitScope splits a scope string by colons
func splitScope(scope string) []string {
	result := []string{}
	current := ""

	for _, char := range scope {
		if char == ':' {
			result = append(result, current)
			current = ""
		} else {
			current += string(char)
		}
	}

	if current != "" {
		result = append(result, current)
	}

	return result
}

// BuildOrgScope creates an organization scope string
func BuildOrgScope(orgID string) string {
	return "org:" + orgID
}

// BuildResourceScope creates a resource scope string
func BuildResourceScope(resourceType, resourceID string) string {
	return "resource:" + resourceType + ":" + resourceID
}

// PermissionBuilder helps build permissions easily
type PermissionBuilder struct {
	userID uuid.UUID
}

// NewPermissionBuilder creates a new permission builder
func NewPermissionBuilder(userID uuid.UUID) *PermissionBuilder {
	return &PermissionBuilder{userID: userID}
}

// SystemAdmin creates system-wide admin permission
func (pb *PermissionBuilder) SystemAdmin() *Permission {
	return &Permission{
		UserID:        pb.userID,
		PermissionKey: BuildPermissionKey(ScopeSystem, ResourceTypeAll, ActionAll),
		ResourceIDs:   pq.StringArray{},
		GrantedAt:     time.Now(),
	}
}

// OrgAdmin creates organization admin permission
func (pb *PermissionBuilder) OrgAdmin(orgID string) *Permission {
	return &Permission{
		UserID:        pb.userID,
		PermissionKey: BuildPermissionKey(BuildOrgScope(orgID), ResourceTypeAll, ActionAll),
		ResourceIDs:   pq.StringArray{},
		GrantedAt:     time.Now(),
	}
}

// OrgResource creates permission for specific resource type in organization
func (pb *PermissionBuilder) OrgResource(orgID, resourceType string, actions ...string) []*Permission {
	permissions := make([]*Permission, len(actions))
	for i, action := range actions {
		permissions[i] = &Permission{
			UserID:        pb.userID,
			PermissionKey: BuildPermissionKey(BuildOrgScope(orgID), resourceType, action),
			ResourceIDs:   pq.StringArray{},
			GrantedAt:     time.Now(),
		}
	}
	return permissions
}

// SpecificResource creates permission for specific resource
func (pb *PermissionBuilder) SpecificResource(resourceType, resourceID string, actions ...string) []*Permission {
	permissions := make([]*Permission, len(actions))
	for i, action := range actions {
		permissions[i] = &Permission{
			UserID:        pb.userID,
			PermissionKey: BuildPermissionKey("resource", resourceType, action),
			ResourceIDs:   pq.StringArray{resourceID},
			GrantedAt:     time.Now(),
		}
	}
	return permissions
}

// Owner creates owner-based permission
func (pb *PermissionBuilder) Owner(resourceType string, actions ...string) []*Permission {
	permissions := make([]*Permission, len(actions))
	for i, action := range actions {
		permissions[i] = &Permission{
			UserID:        pb.userID,
			PermissionKey: BuildPermissionKey(ScopeOwner, resourceType, action),
			ResourceIDs:   pq.StringArray{},
			GrantedAt:     time.Now(),
		}
	}
	return permissions
}
