package auth

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
)

// DualAuthService provides both JWT and API token authentication
type DualAuthService struct {
	// Repositories
	userRepo         repositories.UserRepository
	sessionRepo      repositories.SessionRepository
	apiTokenRepo     repositories.APITokenRepository
	permissionRepo   repositories.PermissionRepository
	usersOrgRepo     repositories.UsersOrganizationRepository
	organizationRepo repositories.OrganizationRepository

	// Services
	cryptoService *crypto.CryptoService

	// Keys for JWT signing/verification
	privateKey string
	publicKey  string
}

// NewDualAuthService creates a new dual authentication service
func NewDualAuthService(
	userRepo repositories.UserRepository,
	sessionRepo repositories.SessionRepository,
	apiTokenRepo repositories.APITokenRepository,
	permissionRepo repositories.PermissionRepository,
	usersOrgRepo repositories.UsersOrganizationRepository,
	organizationRepo repositories.OrganizationRepository,
	cryptoService *crypto.CryptoService,
	privateKey, publicKey string,
) *DualAuthService {
	return &DualAuthService{
		userRepo:         userRepo,
		sessionRepo:      sessionRepo,
		apiTokenRepo:     apiTokenRepo,
		permissionRepo:   permissionRepo,
		usersOrgRepo:     usersOrgRepo,
		organizationRepo: organizationRepo,
		cryptoService:    cryptoService,
		privateKey:       privateKey,
		publicKey:        publicKey,
	}
}

// JWT Token Methods

// GenerateJWTTokenPair generates both access and refresh JWT tokens
// JWT chỉ chứa thông tin cơ bản, permissions sẽ được load từ DB khi cần
func (s *DualAuthService) GenerateJWTTokenPair(ctx context.Context, user *entities.User, session *entities.Session) (accessToken, refreshToken string, err error) {
	// Get user organizations (lightweight data)
	organizations, err := s.usersOrgRepo.GetUserOrganizations(ctx, user.ID)
	if err != nil {
		return "", "", fmt.Errorf("failed to get user organizations: %w", err)
	}

	// Build access token (15 minutes) - chỉ chứa thông tin cơ bản
	accessClaims := entities.NewJWTTokenBuilder(user).
		WithSession(session).
		WithTokenType(entities.JWTTokenTypeAccess).
		WithExpiresIn(15 * time.Minute).
		WithOrganizations(organizations).
		Build() // Không lưu permissions trong JWT

	accessToken, err = s.createJWTFromClaims(accessClaims)
	if err != nil {
		return "", "", fmt.Errorf("failed to create access token: %w", err)
	}

	// Build refresh token (7 days) - chỉ chứa thông tin tối thiểu
	refreshClaims := entities.NewJWTTokenBuilder(user).
		WithSession(session).
		WithTokenType(entities.JWTTokenTypeRefresh).
		WithExpiresIn(7 * 24 * time.Hour).
		Build() // Không có permissions và organizations

	refreshToken, err = s.createJWTFromClaims(refreshClaims)
	if err != nil {
		return "", "", fmt.Errorf("failed to create refresh token: %w", err)
	}

	return accessToken, refreshToken, nil
}

// VerifyJWTToken verifies a JWT token and returns the claims
func (s *DualAuthService) VerifyJWTToken(tokenString string) (*entities.JWTClaims, error) {
	// Verify token signature and get claims
	claimsMap, err := s.cryptoService.VerifyJWTToken(tokenString, s.publicKey)
	if err != nil {
		return nil, fmt.Errorf("failed to verify JWT token: %w", err)
	}

	// Convert map to our claims structure
	claims, err := s.mapToJWTClaims(claimsMap)
	if err != nil {
		return nil, fmt.Errorf("failed to parse JWT claims: %w", err)
	}

	// Validate token is not expired
	if claims.IsExpired() {
		return nil, fmt.Errorf("token has expired")
	}

	return claims, nil
}

// RefreshJWTToken creates a new access token from a valid refresh token
func (s *DualAuthService) RefreshJWTToken(ctx context.Context, refreshToken string) (string, error) {
	// Verify refresh token
	claims, err := s.VerifyJWTToken(refreshToken)
	if err != nil {
		return "", fmt.Errorf("invalid refresh token: %w", err)
	}

	// Ensure it's a refresh token
	if claims.TokenType != entities.JWTTokenTypeRefresh {
		return "", fmt.Errorf("token is not a refresh token")
	}

	// Get user
	userID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return "", fmt.Errorf("invalid user ID in token: %w", err)
	}

	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return "", fmt.Errorf("user not found: %w", err)
	}

	// Get session if present
	var session *entities.Session
	if claims.SessionID != "" {
		sessionID, err := uuid.Parse(claims.SessionID)
		if err == nil {
			session, _ = s.sessionRepo.GetByID(ctx, sessionID)
		}
	}

	// Generate new access token
	accessToken, _, err := s.GenerateJWTTokenPair(ctx, user, session)
	if err != nil {
		return "", fmt.Errorf("failed to generate new access token: %w", err)
	}

	return accessToken, nil
}

// API Token Methods

// CreateAPIToken creates a new API token with specified scopes
func (s *DualAuthService) CreateAPIToken(ctx context.Context, req *CreateAPITokenRequest) (*entities.APIToken, string, error) {
	// Validate request
	if err := s.validateAPITokenRequest(req); err != nil {
		return nil, "", fmt.Errorf("invalid request: %w", err)
	}

	// Create API token entity
	apiToken := &entities.APIToken{
		UserID:         req.UserID,
		OrganizationID: req.OrganizationID,
		Name:           req.Name,
		Description:    req.Description,
		Type:           req.Type,
		Scopes:         req.Scopes,
		Active:         true,
		ExpiresAt:      req.ExpiresAt,
		CreatedByIP:    req.CreatedByIP,
		UserAgent:      req.UserAgent,
	}

	// Generate token string
	tokenString, err := apiToken.GenerateToken()
	if err != nil {
		return nil, "", fmt.Errorf("failed to generate token: %w", err)
	}

	// Save to database
	if err := s.apiTokenRepo.Create(ctx, apiToken); err != nil {
		return nil, "", fmt.Errorf("failed to save API token: %w", err)
	}

	return apiToken, tokenString, nil
}

// ValidateAPIToken validates an API token and returns the token entity
func (s *DualAuthService) ValidateAPIToken(ctx context.Context, tokenString string) (*entities.APIToken, error) {
	// Hash the token to find it in database
	hash := sha256.Sum256([]byte(tokenString))
	tokenHash := hex.EncodeToString(hash[:])

	// Find token by hash
	apiToken, err := s.apiTokenRepo.GetByTokenHash(ctx, tokenHash)
	if err != nil {
		return nil, fmt.Errorf("token not found: %w", err)
	}

	// Check if token is active and not expired
	if !apiToken.IsActive() {
		return nil, fmt.Errorf("token is inactive or expired")
	}

	// Update last used timestamp
	apiToken.UpdateLastUsed("") // IP will be set by middleware
	if err := s.apiTokenRepo.Update(ctx, apiToken); err != nil {
		// Log error but don't fail validation
		// This is not critical for authentication
	}

	return apiToken, nil
}

// RevokeAPIToken revokes an API token
func (s *DualAuthService) RevokeAPIToken(ctx context.Context, tokenID uuid.UUID) error {
	apiToken, err := s.apiTokenRepo.GetByID(ctx, tokenID)
	if err != nil {
		return fmt.Errorf("token not found: %w", err)
	}

	apiToken.Active = false
	return s.apiTokenRepo.Update(ctx, apiToken)
}

// Helper Methods

func (s *DualAuthService) createJWTFromClaims(claims *entities.JWTClaims) (string, error) {
	tokenOpts := crypto.TokenOptions{
		Subject:   claims.Subject,
		Issuer:    claims.Issuer,
		ExpiresIn: time.Until(claims.ExpiresAt),
		JWTID:     claims.JWTID,
		CustomClaims: map[string]interface{}{
			"user_id":         claims.UserID,
			"session_id":      claims.SessionID,
			"organization_id": claims.OrganizationID,
			"token_type":      claims.TokenType,
			"permissions":     claims.Permissions,
			"organizations":   claims.Organizations,
			"ip_address":      claims.IPAddress,
			"user_agent":      claims.UserAgent,
		},
	}

	return s.cryptoService.CreateJWTToken(crypto.SchemeJWTRS256, s.privateKey, tokenOpts)
}

func (s *DualAuthService) mapToJWTClaims(claimsMap map[string]interface{}) (*entities.JWTClaims, error) {
	// This is a simplified version - in production you'd want more robust parsing
	claims := &entities.JWTClaims{}

	if userID, ok := claimsMap["user_id"].(string); ok {
		claims.UserID = userID
	}

	if sessionID, ok := claimsMap["session_id"].(string); ok {
		claims.SessionID = sessionID
	}

	if tokenType, ok := claimsMap["token_type"].(string); ok {
		claims.TokenType = entities.JWTTokenType(tokenType)
	}

	// Parse expiration
	if exp, ok := claimsMap["exp"].(float64); ok {
		claims.ExpiresAt = time.Unix(int64(exp), 0)
	}

	// TODO: Parse permissions and organizations from claims

	return claims, nil
}

func (s *DualAuthService) validateAPITokenRequest(req *CreateAPITokenRequest) error {
	if req.Name == "" {
		return fmt.Errorf("token name is required")
	}

	if req.UserID == nil {
		return fmt.Errorf("user ID is required")
	}

	if len(req.Scopes) == 0 {
		return fmt.Errorf("at least one scope is required")
	}

	return nil
}

// CreateAPITokenRequest represents a request to create an API token
type CreateAPITokenRequest struct {
	UserID         *uuid.UUID               `json:"user_id"`
	OrganizationID *uuid.UUID               `json:"organization_id,omitempty"`
	Name           string                   `json:"name"`
	Description    string                   `json:"description,omitempty"`
	Type           entities.APITokenType    `json:"type"`
	Scopes         []entities.APITokenScope `json:"scopes"`
	ExpiresAt      *time.Time               `json:"expires_at,omitempty"`
	CreatedByIP    string                   `json:"created_by_ip,omitempty"`
	UserAgent      string                   `json:"user_agent,omitempty"`
}
