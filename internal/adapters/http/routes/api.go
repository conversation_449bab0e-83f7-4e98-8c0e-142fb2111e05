package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/handlers"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
)

// APIRoutes manages all API route configurations
type APIRoutes struct {
	authService *auth.AuthService
	authMW      *middleware.AuthenticationMiddleware
	authzMW     *middleware.AuthorizationMiddleware

	// Handlers
	userHandler    *handlers.UserHandler
	orgHandler     *handlers.OrganizationHandler
	productHandler *handlers.ProductHandler
	licenseHandler *handlers.LicenseHandler
	policyHandler  *handlers.PolicyHandler
	machineHandler *handlers.MachineHandler
	authHandler    *handlers.AuthHandler
}

// NewAPIRoutes creates a new API routes instance
func NewAPIRoutes(
	authService *auth.AuthService,
	authMW *middleware.AuthenticationMiddleware,
	authzMW *middleware.AuthorizationMiddleware,
	userHandler *handlers.User<PERSON><PERSON><PERSON>,
	orgHandler *handlers.<PERSON><PERSON><PERSON><PERSON>,
	productHandler *handlers.ProductHandler,
	licenseHandler *handlers.LicenseHandler,
	policyHandler *handlers.PolicyHandler,
	machineHandler *handlers.MachineHandler,
	authHandler *handlers.AuthHandler,
) *APIRoutes {
	return &APIRoutes{
		authService:    authService,
		authMW:         authMW,
		authzMW:        authzMW,
		userHandler:    userHandler,
		orgHandler:     orgHandler,
		productHandler: productHandler,
		licenseHandler: licenseHandler,
		policyHandler:  policyHandler,
		machineHandler: machineHandler,
		authHandler:    authHandler,
	}
}

// SetupRoutes configures all API routes
func (r *APIRoutes) SetupRoutes(router *gin.Engine) {
	// API v1 group
	v1 := router.Group("/api/v1")
	{
		// Public routes (no authentication required)
		r.setupPublicRoutes(v1)

		// TODO: Temporarily disable authentication for testing
		// Protected routes (authentication required)
		// protected := v1.Group("")
		// protected.Use(r.authMW.RequireAuthentication())
		// {
		// 	r.setupAuthRoutes(protected)
		// 	r.setupUserRoutes(protected)
		// 	r.setupOrganizationRoutes(protected)
		// 	r.setupProductRoutes(protected)
		// 	r.setupPolicyRoutes(protected)
		// 	r.setupLicenseRoutes(protected)
		// 	r.setupMachineRoutes(protected)
		// }

		// Temporary: Setup routes without authentication
		r.setupUserRoutesNoAuth(v1)
		r.setupOrganizationRoutesNoAuth(v1)
		r.setupProductRoutesNoAuth(v1)
		r.setupPolicyRoutesNoAuth(v1)
		r.setupLicenseRoutesNoAuth(v1)
		r.setupMachineRoutesNoAuth(v1)
	}
}

// setupPublicRoutes configures public API routes
func (r *APIRoutes) setupPublicRoutes(rg *gin.RouterGroup) {
	// Authentication routes (public)
	auth := rg.Group("/auth")
	{
		auth.POST("/login", r.authHandler.Login)
		// Note: logout, profile, and token management require authentication
	}

	// License validation (public for license holders)
	licenses := rg.Group("/licenses")
	{
		licenses.POST("/:id/actions/validate", r.licenseHandler.ValidateLicense)
		// TODO: Implement checkout and checkin actions
		// licenses.POST("/:id/actions/checkout", r.licenseHandler.CheckoutLicense)
		// licenses.POST("/:id/actions/checkin", r.licenseHandler.CheckinLicense)
	}

	// Machine heartbeat (public for machines)
	machines := rg.Group("/machines")
	{
		machines.POST("/:id/actions/heartbeat", r.machineHandler.UpdateHeartbeat)
		// TODO: Implement ping action
		// machines.POST("/:id/actions/ping", r.machineHandler.PingMachine)
	}
}

// setupAuthRoutes configures authentication-related routes
func (r *APIRoutes) setupAuthRoutes(rg *gin.RouterGroup) {
	auth := rg.Group("/auth")
	{
		auth.POST("/logout", r.authHandler.Logout)
		auth.POST("/refresh", r.authHandler.RefreshToken)
		auth.GET("/profile", r.authHandler.GetProfile)

		// API token management
		tokens := auth.Group("/tokens")
		{
			tokens.GET("", r.authHandler.GetAPITokens)
			tokens.POST("", r.authHandler.GenerateAPIToken)
			tokens.DELETE("/:id", r.authHandler.RevokeAPIToken)
		}
	}
}

// setupUserRoutes configures user management routes
func (r *APIRoutes) setupUserRoutes(rg *gin.RouterGroup) {
	users := rg.Group("/users")
	{
		// List and create users - requires admin permissions
		users.GET("", r.authzMW.RequirePermission("user.read"), r.userHandler.GetUsers)
		users.POST("", r.authzMW.RequirePermission("user.create"), r.userHandler.CreateUser)

		// Individual user operations
		users.GET("/:id", r.authzMW.RequireOwnershipOrPermission("user", "id", "user.read"), r.userHandler.GetUser)
		users.PUT("/:id", r.authzMW.RequireOwnershipOrPermission("user", "id", "user.update"), r.userHandler.UpdateUser)
		users.DELETE("/:id", r.authzMW.RequirePermission("user.delete"), r.userHandler.DeleteUser)
	}
}

// setupOrganizationRoutes configures organization management routes
func (r *APIRoutes) setupOrganizationRoutes(rg *gin.RouterGroup) {
	organizations := rg.Group("/organizations")
	{
		// List and create organizations
		organizations.GET("", r.authzMW.RequirePermission("organization.read"), r.orgHandler.GetOrganizations)
		organizations.POST("", r.authzMW.RequirePermission("organization.create"), r.orgHandler.CreateOrganization)

		// Individual organization operations
		organizations.GET("/:id", r.authzMW.RequireOwnershipOrPermission("organization", "id", "organization.read"), r.orgHandler.GetOrganization)
		organizations.PUT("/:id", r.authzMW.RequireOwnershipOrPermission("organization", "id", "organization.update"), r.orgHandler.UpdateOrganization)
		organizations.DELETE("/:id", r.authzMW.RequirePermission("organization.delete"), r.orgHandler.DeleteOrganization)
	}
}

// setupProductRoutes configures product management routes
func (r *APIRoutes) setupProductRoutes(rg *gin.RouterGroup) {
	products := rg.Group("/products")
	{
		// List and create products
		products.GET("", r.authzMW.RequirePermission("product.read"), r.productHandler.GetProducts)
		products.POST("", r.authzMW.RequirePermission("product.create"), r.productHandler.CreateProduct)

		// Individual product operations
		products.GET("/:id", r.authzMW.RequireOwnershipOrPermission("product", "id", "product.read"), r.productHandler.GetProduct)
		products.PUT("/:id", r.authzMW.RequireOwnershipOrPermission("product", "id", "product.update"), r.productHandler.UpdateProduct)
		products.DELETE("/:id", r.authzMW.RequirePermission("product.delete"), r.productHandler.DeleteProduct)
	}
}

// setupPolicyRoutes configures policy management routes
func (r *APIRoutes) setupPolicyRoutes(rg *gin.RouterGroup) {
	policies := rg.Group("/policies")
	{
		// List and create policies
		policies.GET("", r.authzMW.RequirePermission("policy.read"), r.policyHandler.GetPolicies)
		policies.POST("", r.authzMW.RequirePermission("policy.create"), r.policyHandler.CreatePolicy)

		// Individual policy operations
		policies.GET("/:id", r.authzMW.RequireOwnershipOrPermission("policy", "id", "policy.read"), r.policyHandler.GetPolicy)
		policies.PUT("/:id", r.authzMW.RequireOwnershipOrPermission("policy", "id", "policy.update"), r.policyHandler.UpdatePolicy)
		policies.DELETE("/:id", r.authzMW.RequirePermission("policy.delete"), r.policyHandler.DeletePolicy)
	}
}

// setupLicenseRoutes configures license management routes
func (r *APIRoutes) setupLicenseRoutes(rg *gin.RouterGroup) {
	licenses := rg.Group("/licenses")
	{
		// List and create licenses
		licenses.GET("", r.authzMW.RequirePermission("license.read"), r.licenseHandler.GetLicenses)
		licenses.POST("", r.authzMW.RequirePermission("license.create"), r.licenseHandler.CreateLicense)

		// Individual license operations
		licenses.GET("/:id", r.authzMW.RequireOwnershipOrPermission("license", "id", "license.read"), r.licenseHandler.GetLicense)
		licenses.PUT("/:id", r.authzMW.RequireOwnershipOrPermission("license", "id", "license.update"), r.licenseHandler.UpdateLicense)
		licenses.DELETE("/:id", r.authzMW.RequirePermission("license.delete"), r.licenseHandler.DeleteLicense)

		// License actions (protected)
		licenses.POST("/:id/actions/suspend", r.authzMW.RequirePermission("license.suspend"), r.licenseHandler.SuspendLicense)
		licenses.POST("/:id/actions/reinstate", r.authzMW.RequirePermission("license.reinstate"), r.licenseHandler.ReinstateLicense)
		licenses.POST("/:id/actions/renew", r.authzMW.RequirePermission("license.renew"), r.licenseHandler.RenewLicense)
	}
}

// setupMachineRoutes configures machine management routes
func (r *APIRoutes) setupMachineRoutes(rg *gin.RouterGroup) {
	machines := rg.Group("/machines")
	{
		// List and create machines
		machines.GET("", r.authzMW.RequirePermission("machine.read"), r.machineHandler.GetMachines)
		machines.POST("", r.authzMW.RequirePermission("machine.create"), r.machineHandler.CreateMachine)

		// Individual machine operations
		machines.GET("/:id", r.authzMW.RequireOwnershipOrPermission("machine", "id", "machine.read"), r.machineHandler.GetMachine)
		machines.PUT("/:id", r.authzMW.RequireOwnershipOrPermission("machine", "id", "machine.update"), r.machineHandler.UpdateMachine)
		machines.DELETE("/:id", r.authzMW.RequirePermission("machine.delete"), r.machineHandler.DeleteMachine)

		// Machine actions (protected) - will be implemented later
		// machines.POST("/:id/actions/reset-heartbeat", r.authzMW.RequirePermission("machine.heartbeat.reset"), r.machineHandler.ResetHeartbeat)
	}
}

// Temporary methods without authentication for testing

// setupUserRoutesNoAuth configures user routes without authentication
func (r *APIRoutes) setupUserRoutesNoAuth(rg *gin.RouterGroup) {
	users := rg.Group("/users")
	{
		users.GET("", r.userHandler.GetUsers)
		users.POST("", r.userHandler.CreateUser)
		users.GET("/:id", r.userHandler.GetUser)
		users.PUT("/:id", r.userHandler.UpdateUser)
		users.DELETE("/:id", r.userHandler.DeleteUser)
	}
}

// setupOrganizationRoutesNoAuth configures organization routes without authentication
func (r *APIRoutes) setupOrganizationRoutesNoAuth(rg *gin.RouterGroup) {
	organizations := rg.Group("/organizations")
	{
		organizations.GET("", r.orgHandler.GetOrganizations)
		organizations.POST("", r.orgHandler.CreateOrganization)
		organizations.GET("/:id", r.orgHandler.GetOrganization)
		organizations.PUT("/:id", r.orgHandler.UpdateOrganization)
		organizations.DELETE("/:id", r.orgHandler.DeleteOrganization)
	}
}

// setupProductRoutesNoAuth configures product routes without authentication
func (r *APIRoutes) setupProductRoutesNoAuth(rg *gin.RouterGroup) {
	products := rg.Group("/products")
	{
		products.GET("", r.productHandler.GetProducts)
		products.POST("", r.productHandler.CreateProduct)
		products.GET("/:id", r.productHandler.GetProduct)
		products.PUT("/:id", r.productHandler.UpdateProduct)
		products.DELETE("/:id", r.productHandler.DeleteProduct)
	}
}

// setupPolicyRoutesNoAuth configures policy routes without authentication
func (r *APIRoutes) setupPolicyRoutesNoAuth(rg *gin.RouterGroup) {
	policies := rg.Group("/policies")
	{
		policies.GET("", r.policyHandler.GetPolicies)
		policies.POST("", r.policyHandler.CreatePolicy)
		policies.GET("/:id", r.policyHandler.GetPolicy)
		policies.PUT("/:id", r.policyHandler.UpdatePolicy)
		policies.DELETE("/:id", r.policyHandler.DeletePolicy)
	}
}

// setupLicenseRoutesNoAuth configures license routes without authentication
func (r *APIRoutes) setupLicenseRoutesNoAuth(rg *gin.RouterGroup) {
	licenses := rg.Group("/licenses")
	{
		licenses.GET("", r.licenseHandler.GetLicenses)
		licenses.POST("", r.licenseHandler.CreateLicense)
		licenses.GET("/:id", r.licenseHandler.GetLicense)
		licenses.PUT("/:id", r.licenseHandler.UpdateLicense)
		licenses.DELETE("/:id", r.licenseHandler.DeleteLicense)

		// License actions
		licenses.POST("/:id/actions/suspend", r.licenseHandler.SuspendLicense)
		licenses.POST("/:id/actions/reinstate", r.licenseHandler.ReinstateLicense)
		licenses.POST("/:id/actions/renew", r.licenseHandler.RenewLicense)
	}
}

// setupMachineRoutesNoAuth configures machine routes without authentication
func (r *APIRoutes) setupMachineRoutesNoAuth(rg *gin.RouterGroup) {
	machines := rg.Group("/machines")
	{
		machines.GET("", r.machineHandler.GetMachines)
		machines.POST("", r.machineHandler.CreateMachine)
		machines.GET("/:id", r.machineHandler.GetMachine)
		machines.PUT("/:id", r.machineHandler.UpdateMachine)
		machines.DELETE("/:id", r.machineHandler.DeleteMachine)
	}
}
