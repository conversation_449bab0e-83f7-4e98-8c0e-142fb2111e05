package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// AuthorizationMiddleware handles authorization for HTTP requests
type AuthorizationMiddleware struct {
	authService *auth.AuthService
}

// NewAuthorizationMiddleware creates a new authorization middleware
func NewAuthorizationMiddleware(authService *auth.AuthService) *AuthorizationMiddleware {
	return &AuthorizationMiddleware{
		authService: authService,
	}
}

// RequirePermission creates middleware that requires specific permissions
func (am *AuthorizationMiddleware) RequirePermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, exists := GetAuthContext(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": gin.H{
					"code":    "AUTHENTICATION_REQUIRED",
					"message": "Authentication is required for this endpoint",
				},
			})
			c.Abort()
			return
		}

		// Check if user has any of the required permissions
		hasPermission := false
		for _, permission := range permissions {
			allowed, err := am.authService.CheckPermission(
				c.Request.Context(),
				authCtx.User.ID.String(),
				am.extractResourceType(permission),
				am.extractAction(permission),
				nil, // resourceID - will be extracted from URL params if needed
				nil, // organizationID - will be extracted from context if needed
			)
			if err != nil {
				log.Error().Err(err).Str("permission", permission).Msg("Failed to check permission")
				continue
			}
			if allowed {
				hasPermission = true
				break
			}
		}

		// Also check API token permissions if using API token auth
		if !hasPermission && authCtx.AuthType == "api_token" && authCtx.APIToken != nil {
			for _, permission := range permissions {
				if authCtx.APIToken.HasPermission(permission) {
					hasPermission = true
					break
				}
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error": gin.H{
					"code":    "INSUFFICIENT_PERMISSIONS",
					"message": fmt.Sprintf("One of the following permissions is required: %v", permissions),
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireResourceOwnership creates middleware that requires ownership of a specific resource
func (am *AuthorizationMiddleware) RequireResourceOwnership(resourceType, paramName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, exists := GetAuthContext(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": gin.H{
					"code":    "AUTHENTICATION_REQUIRED",
					"message": "Authentication is required for this endpoint",
				},
			})
			c.Abort()
			return
		}

		resourceID := c.Param(paramName)
		if resourceID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": gin.H{
					"code":    "MISSING_RESOURCE_ID",
					"message": fmt.Sprintf("Resource ID parameter '%s' is required", paramName),
				},
			})
			c.Abort()
			return
		}

		// Check if user owns the resource
		allowed, err := am.authService.CheckPermission(
			c.Request.Context(),
			authCtx.User.ID.String(),
			resourceType,
			"read", // Basic read permission for ownership check
			&resourceID,
			nil,
		)
		if err != nil {
			log.Error().Err(err).Str("resource_type", resourceType).Str("resource_id", resourceID).Msg("Failed to check resource ownership")
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": gin.H{
					"code":    "AUTHORIZATION_ERROR",
					"message": "Failed to verify resource ownership",
				},
			})
			c.Abort()
			return
		}

		if !allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error": gin.H{
					"code":    "RESOURCE_ACCESS_DENIED",
					"message": "You do not have access to this resource",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireOwnershipOrPermission creates middleware that requires either resource ownership or specific permissions
func (am *AuthorizationMiddleware) RequireOwnershipOrPermission(resourceType, paramName string, permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, exists := GetAuthContext(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": gin.H{
					"code":    "AUTHENTICATION_REQUIRED",
					"message": "Authentication is required for this endpoint",
				},
			})
			c.Abort()
			return
		}

		resourceID := c.Param(paramName)
		if resourceID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": gin.H{
					"code":    "MISSING_RESOURCE_ID",
					"message": fmt.Sprintf("Resource ID parameter '%s' is required", paramName),
				},
			})
			c.Abort()
			return
		}

		// First check if user has general permissions
		hasPermission := false
		for _, permission := range permissions {
			allowed, err := am.authService.CheckPermission(
				c.Request.Context(),
				authCtx.User.ID.String(),
				am.extractResourceType(permission),
				am.extractAction(permission),
				nil,
				nil,
			)
			if err != nil {
				log.Error().Err(err).Str("permission", permission).Msg("Failed to check permission")
				continue
			}
			if allowed {
				hasPermission = true
				break
			}
		}

		// If no general permission, check resource ownership
		if !hasPermission {
			allowed, err := am.authService.CheckPermission(
				c.Request.Context(),
				authCtx.User.ID.String(),
				resourceType,
				"read",
				&resourceID,
				nil,
			)
			if err != nil {
				log.Error().Err(err).Str("resource_type", resourceType).Str("resource_id", resourceID).Msg("Failed to check resource ownership")
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": gin.H{
						"code":    "AUTHORIZATION_ERROR",
						"message": "Failed to verify permissions",
					},
				})
				c.Abort()
				return
			}
			hasPermission = allowed
		}

		// Also check API token permissions if using API token auth
		if !hasPermission && authCtx.AuthType == "api_token" && authCtx.APIToken != nil {
			for _, permission := range permissions {
				if authCtx.APIToken.HasPermission(permission) {
					hasPermission = true
					break
				}
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error": gin.H{
					"code":    "INSUFFICIENT_PERMISSIONS",
					"message": "You do not have permission to access this resource",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireOrganizationAccess creates middleware that requires access to a specific organization
func (am *AuthorizationMiddleware) RequireOrganizationAccess(paramName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, exists := GetAuthContext(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": gin.H{
					"code":    "AUTHENTICATION_REQUIRED",
					"message": "Authentication is required for this endpoint",
				},
			})
			c.Abort()
			return
		}

		organizationID := c.Param(paramName)
		if organizationID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": gin.H{
					"code":    "MISSING_ORGANIZATION_ID",
					"message": fmt.Sprintf("Organization ID parameter '%s' is required", paramName),
				},
			})
			c.Abort()
			return
		}

		// Validate organization ID format
		if _, err := uuid.Parse(organizationID); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": gin.H{
					"code":    "INVALID_ORGANIZATION_ID",
					"message": "Invalid organization ID format",
				},
			})
			c.Abort()
			return
		}

		// Check if user has access to the organization
		allowed, err := am.authService.CheckPermission(
			c.Request.Context(),
			authCtx.User.ID.String(),
			"organization",
			"read",
			&organizationID,
			&organizationID,
		)
		if err != nil {
			log.Error().Err(err).Str("organization_id", organizationID).Msg("Failed to check organization access")
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": gin.H{
					"code":    "AUTHORIZATION_ERROR",
					"message": "Failed to verify organization access",
				},
			})
			c.Abort()
			return
		}

		if !allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error": gin.H{
					"code":    "ORGANIZATION_ACCESS_DENIED",
					"message": "You do not have access to this organization",
				},
			})
			c.Abort()
			return
		}

		// Store organization ID in context for use by handlers
		c.Set("organization_id", organizationID)
		c.Next()
	}
}

// extractResourceType extracts resource type from permission string (e.g., "license.read" -> "license")
func (am *AuthorizationMiddleware) extractResourceType(permission string) string {
	parts := strings.Split(permission, ".")
	if len(parts) > 0 {
		return parts[0]
	}
	return permission
}

// extractAction extracts action from permission string (e.g., "license.read" -> "read")
func (am *AuthorizationMiddleware) extractAction(permission string) string {
	parts := strings.Split(permission, ".")
	if len(parts) > 1 {
		return parts[1]
	}
	return "read" // default action
}

// GetOrganizationID retrieves organization ID from context
func GetOrganizationID(c *gin.Context) (string, bool) {
	orgID, exists := c.Get("organization_id")
	if !exists {
		return "", false
	}
	id, ok := orgID.(string)
	return id, ok
}
