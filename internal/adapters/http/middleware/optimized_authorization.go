package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/database/postgres/repositories"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// OptimizedAuthorizationMiddleware provides ultra-high-performance authorization
type OptimizedAuthorizationMiddleware struct {
	permissionRepo *repositories.OptimizedPermissionRepository
}

// NewOptimizedAuthorizationMiddleware creates a new optimized authorization middleware
func NewOptimizedAuthorizationMiddleware(permissionRepo *repositories.OptimizedPermissionRepository) *OptimizedAuthorizationMiddleware {
	return &OptimizedAuthorizationMiddleware{
		permissionRepo: permissionRepo,
	}
}

// OptimizedPermissionLookupRequest represents an optimized permission lookup
type OptimizedPermissionLookupRequest struct {
	UserID       string
	Scope        string
	ResourceType string
	Action       string
	ResourceID   *string
}

// RequirePermissions creates ultra-optimized middleware for permission checking
func (am *OptimizedAuthorizationMiddleware) RequirePermissions(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, exists := GetAuthContext(c)
		if !exists || authCtx == nil {
			am.respondUnauthorized(c, "Authentication required")
			return
		}

		// Build optimized permission lookup requests
		requests := make([]*repositories.PermissionLookupRequest, len(permissions))
		orgID := am.getOrganizationIDString(c, authCtx)

		for i, permission := range permissions {
			// Check if it's already a full permission key format
			if am.isFullPermissionKey(permission) {
				// Parse full key: "org:company-abc:product:read"
				parts := strings.Split(permission, ":")
				if len(parts) >= 3 {
					requests[i] = &repositories.PermissionLookupRequest{
						UserID:       authCtx.User.ID.String(),
						Scope:        parts[0] + ":" + parts[1], // "org:company-abc"
						ResourceType: parts[2],                  // "product"
						Action:       parts[3],                  // "read"
					}
				}
			} else {
				// Legacy format: "product.read" - build full permission key
				resourceType, action := am.parsePermission(permission)
				scope := "resource"
				if orgID != "" {
					scope = fmt.Sprintf("org:%s", orgID)
				}

				requests[i] = &repositories.PermissionLookupRequest{
					UserID:       authCtx.User.ID.String(),
					Scope:        scope,
					ResourceType: resourceType,
					Action:       action,
				}
			}
		}

		// Batch check all permissions in single query
		results, err := am.permissionRepo.BatchHasPermissionsOptimized(c.Request.Context(), authCtx.User.ID.String(), requests)
		if err != nil {
			log.Error().Err(err).Str("user_id", authCtx.User.ID.String()).Msg("Failed to check permissions (optimized)")
			am.respondInternalError(c, "Permission check failed")
			return
		}

		// Check if any permission is granted
		hasPermission := false
		for _, allowed := range results {
			if allowed {
				hasPermission = true
				break
			}
		}

		// Also check API token permissions if using API token auth
		if !hasPermission && authCtx.AuthType == "api_token" && authCtx.APIToken != nil {
			hasPermission = am.checkAPITokenPermissions(authCtx.APIToken, permissions)
		}

		if !hasPermission {
			am.respondForbidden(c, fmt.Sprintf("One of the following permissions is required: %v", permissions))
			return
		}

		// Store optimization info in context
		c.Set("permission_check_optimized", true)
		c.Next()
	}
}

// RequireResourceOwnership creates ultra-optimized middleware for resource ownership
func (am *OptimizedAuthorizationMiddleware) RequireResourceOwnership(resourceType, paramName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, exists := GetAuthContext(c)
		if !exists || authCtx == nil {
			am.respondUnauthorized(c, "Authentication required")
			return
		}

		resourceID := c.Param(paramName)
		if resourceID == "" {
			am.respondBadRequest(c, fmt.Sprintf("Resource ID parameter '%s' is required", paramName))
			return
		}

		// Ultra-optimized permission lookup
		orgID := am.getOrganizationIDString(c, authCtx)
		scope := "resource"
		if orgID != "" {
			scope = fmt.Sprintf("org:%s", orgID)
		}

		lookupReq := &repositories.PermissionLookupRequest{
			UserID:       authCtx.User.ID.String(),
			Scope:        scope,
			ResourceType: resourceType,
			Action:       "read",
			ResourceID:   &resourceID,
		}

		allowed, err := am.permissionRepo.HasPermissionOptimized(c.Request.Context(), lookupReq)
		if err != nil {
			log.Error().Err(err).
				Str("user_id", authCtx.User.ID.String()).
				Str("resource_type", resourceType).
				Str("resource_id", resourceID).
				Msg("Failed to check resource ownership (optimized)")
			am.respondInternalError(c, "Failed to verify resource ownership")
			return
		}

		if !allowed {
			am.respondForbidden(c, "You do not have access to this resource")
			return
		}

		// Store optimization info in context
		c.Set("permission_check_optimized", true)
		c.Set("resource_id", resourceID)
		c.Next()
	}
}

// RequireOrganizationAccess creates optimized middleware for organization access
func (am *OptimizedAuthorizationMiddleware) RequireOrganizationAccess(paramName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, exists := GetAuthContext(c)
		if !exists || authCtx == nil {
			am.respondUnauthorized(c, "Authentication required")
			return
		}

		organizationID := c.Param(paramName)
		if organizationID == "" {
			am.respondBadRequest(c, fmt.Sprintf("Organization ID parameter '%s' is required", paramName))
			return
		}

		// Validate organization ID format
		if _, err := uuid.Parse(organizationID); err != nil {
			am.respondBadRequest(c, "Invalid organization ID format")
			return
		}

		// Optimized organization access check
		lookupReq := &repositories.PermissionLookupRequest{
			UserID:       authCtx.User.ID.String(),
			Scope:        fmt.Sprintf("org:%s", organizationID),
			ResourceType: "organization",
			Action:       "read",
			ResourceID:   &organizationID,
		}

		allowed, err := am.permissionRepo.HasPermissionOptimized(c.Request.Context(), lookupReq)
		if err != nil {
			log.Error().Err(err).
				Str("user_id", authCtx.User.ID.String()).
				Str("organization_id", organizationID).
				Msg("Failed to check organization access (optimized)")
			am.respondInternalError(c, "Failed to verify organization access")
			return
		}

		if !allowed {
			am.respondForbidden(c, "You do not have access to this organization")
			return
		}

		// Store organization ID and optimization info in context
		c.Set("organization_id", organizationID)
		c.Set("permission_check_optimized", true)
		c.Next()
	}
}

// RequireAnyPermission creates middleware that requires any of the specified permissions
func (am *OptimizedAuthorizationMiddleware) RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return am.RequirePermissions(permissions...)
}

// RequireAllPermissions creates middleware that requires all specified permissions
func (am *OptimizedAuthorizationMiddleware) RequireAllPermissions(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, exists := GetAuthContext(c)
		if !exists || authCtx == nil {
			am.respondUnauthorized(c, "Authentication required")
			return
		}

		// Build permission lookup requests
		requests := make([]*repositories.PermissionLookupRequest, len(permissions))
		orgID := am.getOrganizationIDString(c, authCtx)

		for i, permission := range permissions {
			resourceType, action := am.parsePermission(permission)
			scope := "resource"
			if orgID != "" {
				scope = fmt.Sprintf("org:%s", orgID)
			}

			requests[i] = &repositories.PermissionLookupRequest{
				UserID:       authCtx.User.ID.String(),
				Scope:        scope,
				ResourceType: resourceType,
				Action:       action,
			}
		}

		// Batch check all permissions
		results, err := am.permissionRepo.BatchHasPermissionsOptimized(c.Request.Context(), authCtx.User.ID.String(), requests)
		if err != nil {
			log.Error().Err(err).Str("user_id", authCtx.User.ID.String()).Msg("Failed to check permissions (optimized)")
			am.respondInternalError(c, "Permission check failed")
			return
		}

		// Check that ALL permissions are granted
		for i, allowed := range results {
			if !allowed {
				am.respondForbidden(c, fmt.Sprintf("Missing required permission: %s", permissions[i]))
				return
			}
		}

		c.Set("permission_check_optimized", true)
		c.Next()
	}
}

// Helper methods

func (am *OptimizedAuthorizationMiddleware) parsePermission(permission string) (resourceType, action string) {
	parts := strings.Split(permission, ".")
	if len(parts) >= 2 {
		return parts[0], parts[1]
	}
	return permission, "read" // default action
}

// isFullPermissionKey checks if permission is in full key format (scope:resource_type:action)
func (am *OptimizedAuthorizationMiddleware) isFullPermissionKey(permission string) bool {
	parts := strings.Split(permission, ":")
	return len(parts) >= 3 && (strings.HasPrefix(permission, "system:") ||
		strings.HasPrefix(permission, "org:") ||
		strings.HasPrefix(permission, "resource:") ||
		strings.HasPrefix(permission, "owner:"))
}

func (am *OptimizedAuthorizationMiddleware) getOrganizationIDString(c *gin.Context, authCtx *AuthContext) string {
	// Try to get from URL parameter first
	if orgID := c.Param("organization_id"); orgID != "" {
		return orgID
	}
	if orgID := c.Param("org_id"); orgID != "" {
		return orgID
	}

	// Try to get from JWT claims
	if authCtx.JWTClaims != nil && authCtx.JWTClaims.OrganizationID != nil {
		return *authCtx.JWTClaims.OrganizationID
	}

	// Try to get from context (set by previous middleware)
	if orgID, exists := c.Get("organization_id"); exists {
		if id, ok := orgID.(string); ok {
			return id
		}
	}

	return ""
}

func (am *OptimizedAuthorizationMiddleware) checkAPITokenPermissions(apiToken *entities.APIToken, permissions []string) bool {
	for _, permission := range permissions {
		resourceType, action := am.parsePermission(permission)

		// Check if API token has scope for this permission
		for _, scope := range apiToken.Scopes {
			if am.scopeMatches(scope, resourceType, action) {
				return true
			}
		}
	}
	return false
}

func (am *OptimizedAuthorizationMiddleware) scopeMatches(scope entities.APITokenScope, resourceType, action string) bool {
	// Check resource type match
	if scope.ResourceType != "*" && scope.ResourceType != resourceType {
		return false
	}

	// Check action match
	for _, allowedAction := range scope.Actions {
		if allowedAction == "*" || allowedAction == action {
			return true
		}
	}

	return false
}

// Response helper methods

func (am *OptimizedAuthorizationMiddleware) respondUnauthorized(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, gin.H{
		"error": gin.H{
			"code":    "AUTHENTICATION_REQUIRED",
			"message": message,
		},
	})
	c.Abort()
}

func (am *OptimizedAuthorizationMiddleware) respondForbidden(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, gin.H{
		"error": gin.H{
			"code":    "INSUFFICIENT_PERMISSIONS",
			"message": message,
		},
	})
	c.Abort()
}

func (am *OptimizedAuthorizationMiddleware) respondBadRequest(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, gin.H{
		"error": gin.H{
			"code":    "INVALID_REQUEST",
			"message": message,
		},
	})
	c.Abort()
}

func (am *OptimizedAuthorizationMiddleware) respondInternalError(c *gin.Context, message string) {
	c.JSON(http.StatusInternalServerError, gin.H{
		"error": gin.H{
			"code":    "INTERNAL_ERROR",
			"message": message,
		},
	})
	c.Abort()
}
