package repositories

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// OptimizedPermissionRepository implements high-performance permission operations
type OptimizedPermissionRepository struct {
	db *gorm.DB
}

// NewOptimizedPermissionRepository creates a new optimized permission repository
func NewOptimizedPermissionRepository(db *gorm.DB) repositories.PermissionRepository {
	return &OptimizedPermissionRepository{db: db}
}

// PermissionLookupRequest represents a permission lookup request
type PermissionLookupRequest struct {
	UserID       string
	Scope        string
	ResourceType string
	Action       string
	ResourceID   *string
}

// HasPermissionOptimized performs O(1) permission lookup using flattened keys
func (r *OptimizedPermissionRepository) HasPermissionOptimized(ctx context.Context, req *PermissionLookupRequest) (bool, error) {
	// Build permission keys to check in hierarchical order
	permissionKeys := r.buildPermissionKeysHierarchy(req)
	
	// Single query to check all possible permission keys
	var count int64
	query := r.db.WithContext(ctx).
		Model(&entities.Permission{}).
		Where("user_id = ?", req.UserID).
		Where("permission_key IN ?", permissionKeys).
		Where("expires_at IS NULL OR expires_at > ?", time.Now())

	// If checking specific resource, add resource ID filter
	if req.ResourceID != nil {
		query = query.Where("resource_ids = '[]' OR ? = ANY(resource_ids)", *req.ResourceID)
	}

	err := query.Count(&count).Error
	return count > 0, err
}

// buildPermissionKeysHierarchy creates permission keys in hierarchical order
func (r *OptimizedPermissionRepository) buildPermissionKeysHierarchy(req *PermissionLookupRequest) []string {
	keys := make([]string, 0, 8)
	
	// 1. System-wide permissions (highest priority)
	keys = append(keys, entities.BuildPermissionKey("system", "*", "*"))
	keys = append(keys, entities.BuildPermissionKey("system", req.ResourceType, "*"))
	keys = append(keys, entities.BuildPermissionKey("system", "*", req.Action))
	keys = append(keys, entities.BuildPermissionKey("system", req.ResourceType, req.Action))
	
	// 2. Organization permissions
	if strings.HasPrefix(req.Scope, "org:") {
		keys = append(keys, entities.BuildPermissionKey(req.Scope, "*", "*"))
		keys = append(keys, entities.BuildPermissionKey(req.Scope, req.ResourceType, "*"))
		keys = append(keys, entities.BuildPermissionKey(req.Scope, "*", req.Action))
		keys = append(keys, entities.BuildPermissionKey(req.Scope, req.ResourceType, req.Action))
	}
	
	// 3. Resource-specific permissions
	keys = append(keys, entities.BuildPermissionKey("resource", "*", "*"))
	keys = append(keys, entities.BuildPermissionKey("resource", req.ResourceType, "*"))
	keys = append(keys, entities.BuildPermissionKey("resource", "*", req.Action))
	keys = append(keys, entities.BuildPermissionKey("resource", req.ResourceType, req.Action))
	
	// 4. Owner permissions
	keys = append(keys, entities.BuildPermissionKey("owner", "*", "*"))
	keys = append(keys, entities.BuildPermissionKey("owner", req.ResourceType, "*"))
	keys = append(keys, entities.BuildPermissionKey("owner", "*", req.Action))
	keys = append(keys, entities.BuildPermissionKey("owner", req.ResourceType, req.Action))
	
	return keys
}

// GetUserPermissionsOptimized gets user permissions with optimized query
func (r *OptimizedPermissionRepository) GetUserPermissionsOptimized(ctx context.Context, userID string) ([]*entities.Permission, error) {
	var permissions []*entities.Permission

	err := r.db.WithContext(ctx).
		Select("id, user_id, permission_key, resource_ids, granted_by, granted_at, expires_at").
		Where("user_id = ?", userID).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Order("permission_key ASC"). // Order for better cache locality
		Find(&permissions).Error

	return permissions, err
}

// BatchHasPermissionsOptimized checks multiple permissions in a single query
func (r *OptimizedPermissionRepository) BatchHasPermissionsOptimized(ctx context.Context, userID string, requests []*PermissionLookupRequest) ([]bool, error) {
	if len(requests) == 0 {
		return []bool{}, nil
	}

	// Build all permission keys for all requests
	allKeys := make([]string, 0)
	keyToRequestMap := make(map[string][]int) // permission_key -> request indices
	
	for i, req := range requests {
		req.UserID = userID // Ensure userID is set
		keys := r.buildPermissionKeysHierarchy(req)
		for _, key := range keys {
			allKeys = append(allKeys, key)
			keyToRequestMap[key] = append(keyToRequestMap[key], i)
		}
	}

	// Single query to get all matching permissions
	var permissions []entities.Permission
	err := r.db.WithContext(ctx).
		Select("permission_key, resource_ids").
		Where("user_id = ?", userID).
		Where("permission_key IN ?", allKeys).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Find(&permissions).Error

	if err != nil {
		return nil, err
	}

	// Map results back to requests
	results := make([]bool, len(requests))
	for _, perm := range permissions {
		if requestIndices, exists := keyToRequestMap[perm.PermissionKey]; exists {
			for _, reqIndex := range requestIndices {
				req := requests[reqIndex]
				
				// Check resource ID constraint
				if req.ResourceID != nil {
					if len(perm.ResourceIDs) == 0 || r.containsResourceID(perm.ResourceIDs, *req.ResourceID) {
						results[reqIndex] = true
					}
				} else {
					results[reqIndex] = true
				}
			}
		}
	}

	return results, nil
}

// containsResourceID checks if resource ID is in the allowed list
func (r *OptimizedPermissionRepository) containsResourceID(resourceIDs []string, resourceID string) bool {
	for _, id := range resourceIDs {
		if id == resourceID {
			return true
		}
	}
	return false
}

// GrantPermissionOptimized grants a permission using flattened key
func (r *OptimizedPermissionRepository) GrantPermissionOptimized(ctx context.Context, userID uuid.UUID, scope, resourceType, action string, resourceIDs []string, grantedBy *uuid.UUID, expiresAt *time.Time) (*entities.Permission, error) {
	permission := &entities.Permission{
		UserID:        userID,
		PermissionKey: entities.BuildPermissionKey(scope, resourceType, action),
		ResourceIDs:   resourceIDs,
		GrantedBy:     grantedBy,
		GrantedAt:     time.Now(),
		ExpiresAt:     expiresAt,
	}

	err := r.db.WithContext(ctx).Create(permission).Error
	if err != nil {
		return nil, err
	}

	return permission, nil
}

// RevokePermissionOptimized revokes a permission using flattened key
func (r *OptimizedPermissionRepository) RevokePermissionOptimized(ctx context.Context, userID, scope, resourceType, action string) error {
	permissionKey := entities.BuildPermissionKey(scope, resourceType, action)
	
	return r.db.WithContext(ctx).
		Where("user_id = ? AND permission_key = ?", userID, permissionKey).
		Delete(&entities.Permission{}).Error
}

// GetPermissionsByPattern gets permissions matching a key pattern
func (r *OptimizedPermissionRepository) GetPermissionsByPattern(ctx context.Context, userID, keyPattern string) ([]*entities.Permission, error) {
	var permissions []*entities.Permission

	query := r.db.WithContext(ctx).
		Select("id, user_id, permission_key, resource_ids, granted_by, granted_at, expires_at").
		Where("user_id = ?", userID).
		Where("expires_at IS NULL OR expires_at > ?", time.Now())

	// Add pattern matching
	if strings.Contains(keyPattern, "%") {
		query = query.Where("permission_key LIKE ?", keyPattern)
	} else {
		query = query.Where("permission_key = ?", keyPattern)
	}

	err := query.Order("permission_key ASC").Find(&permissions).Error
	return permissions, err
}

// GetUserPermissionKeys gets just the permission keys for a user (for caching)
func (r *OptimizedPermissionRepository) GetUserPermissionKeys(ctx context.Context, userID string) (map[string][]string, error) {
	var results []struct {
		PermissionKey string   `json:"permission_key"`
		ResourceIDs   []string `json:"resource_ids"`
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Permission{}).
		Select("permission_key, resource_ids").
		Where("user_id = ?", userID).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	// Convert to map for O(1) lookup
	permissionMap := make(map[string][]string)
	for _, result := range results {
		permissionMap[result.PermissionKey] = result.ResourceIDs
	}

	return permissionMap, nil
}

// Backward compatibility methods - implement the original interface

func (r *OptimizedPermissionRepository) Create(ctx context.Context, permission *entities.Permission) error {
	return r.db.WithContext(ctx).Create(permission).Error
}

func (r *OptimizedPermissionRepository) GetByID(ctx context.Context, id string) (*entities.Permission, error) {
	var permission entities.Permission
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&permission).Error
	return &permission, err
}

func (r *OptimizedPermissionRepository) Update(ctx context.Context, permission *entities.Permission) error {
	return r.db.WithContext(ctx).Save(permission).Error
}

func (r *OptimizedPermissionRepository) Delete(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&entities.Permission{}, "id = ?", id).Error
}

func (r *OptimizedPermissionRepository) GetUserPermissions(ctx context.Context, userID string) ([]*entities.Permission, error) {
	return r.GetUserPermissionsOptimized(ctx, userID)
}

func (r *OptimizedPermissionRepository) GetPermissionsByScope(ctx context.Context, scopePattern string) ([]*entities.Permission, error) {
	var permissions []*entities.Permission
	keyPattern := scopePattern + ":%:%"
	
	err := r.db.WithContext(ctx).
		Where("permission_key LIKE ?", keyPattern).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Find(&permissions).Error

	return permissions, err
}

func (r *OptimizedPermissionRepository) GetUsersWithPermission(ctx context.Context, scope, resourceType, action string) ([]*entities.Permission, error) {
	permissionKey := entities.BuildPermissionKey(scope, resourceType, action)
	
	var permissions []*entities.Permission
	err := r.db.WithContext(ctx).
		Where("permission_key = ?", permissionKey).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Preload("User").
		Find(&permissions).Error

	return permissions, err
}

func (r *OptimizedPermissionRepository) GrantPermission(ctx context.Context, userID uuid.UUID, scope, resourceType string, actions []string, grantedBy *uuid.UUID, expiresAt *time.Time) (*entities.Permission, error) {
	// For backward compatibility, grant the first action
	if len(actions) == 0 {
		return nil, fmt.Errorf("no actions specified")
	}
	
	return r.GrantPermissionOptimized(ctx, userID, scope, resourceType, actions[0], []string{}, grantedBy, expiresAt)
}

func (r *OptimizedPermissionRepository) RevokePermission(ctx context.Context, userID, scope, resourceType string) error {
	// Revoke all actions for this scope and resource type
	keyPattern := entities.BuildPermissionKey(scope, resourceType, "%")
	
	return r.db.WithContext(ctx).
		Where("user_id = ? AND permission_key LIKE ?", userID, keyPattern).
		Delete(&entities.Permission{}).Error
}

func (r *OptimizedPermissionRepository) RevokeUserPermissions(ctx context.Context, userID string) error {
	return r.db.WithContext(ctx).Where("user_id = ?", userID).Delete(&entities.Permission{}).Error
}

func (r *OptimizedPermissionRepository) GetExpiredPermissions(ctx context.Context) ([]*entities.Permission, error) {
	var permissions []*entities.Permission
	err := r.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now()).
		Find(&permissions).Error
	return permissions, err
}

func (r *OptimizedPermissionRepository) CleanExpiredPermissions(ctx context.Context) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now()).
		Delete(&entities.Permission{})
	return result.RowsAffected, result.Error
}

func (r *OptimizedPermissionRepository) HasPermission(ctx context.Context, userID, scope, resourceType, action string) (bool, error) {
	req := &PermissionLookupRequest{
		UserID:       userID,
		Scope:        scope,
		ResourceType: resourceType,
		Action:       action,
	}
	return r.HasPermissionOptimized(ctx, req)
}
