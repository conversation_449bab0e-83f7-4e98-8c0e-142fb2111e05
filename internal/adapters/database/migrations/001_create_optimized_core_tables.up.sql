-- Optimized core database tables for gokeys license management system
-- Simplified schema with full cryptographic support and OPA compatibility
-- Created: 2025-07-23

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ==================================================
-- HELPER FUNCTIONS for permission validation
-- ==================================================

-- Function to validate organization ID in scope
CREATE OR REPLACE FUNCTION validate_org_scope(scope_value TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- If scope is not org-scoped, it's valid
    IF scope_value NOT LIKE 'org:%' THEN
        RETURN TRUE;
    END IF;

    -- Extract organization ID from scope and validate it exists
    DECLARE
        org_id UUID;
    BEGIN
        org_id := CAST(substring(scope_value from 5) AS UUID);
        RETURN EXISTS(SELECT 1 FROM organizations WHERE id = org_id AND deleted_at IS NULL);
    EXCEPTION
        WHEN invalid_text_representation THEN
            RETURN FALSE;
    END;
END;
$$ LANGUAGE plpgsql;

-- ==================================================
-- ORGANIZATIONS - Vendors, Resellers, Enterprise Customers
-- ==================================================
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    
    -- Organization type
    type VARCHAR(50) DEFAULT 'vendor' CHECK (type IN ('vendor', 'reseller', 'customer')),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'canceled')),
    protected BOOLEAN DEFAULT FALSE,
    
    -- Cryptographic keys (encrypted at rest) - FULL SUPPORT
    public_key TEXT, -- RSA public key
    private_key TEXT, -- RSA private key (encrypted)
    secret_key TEXT, -- 128-char hex secret
    ed25519_private_key TEXT, -- Ed25519 private key (encrypted)
    ed25519_public_key TEXT, -- Ed25519 public key
    
    -- Resource limits
    max_users INTEGER,
    max_licenses INTEGER,
    max_machines INTEGER,
    
    -- Configuration
    settings JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- ==================================================
-- USERS - Independent users (no forced organization)
-- ==================================================
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    
    -- Simple status
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    
    -- 2FA support
    totp_secret TEXT,
    totp_enabled BOOLEAN DEFAULT FALSE,
    
    -- Password reset
    password_reset_token VARCHAR(255),
    password_reset_expires_at TIMESTAMP WITH TIME ZONE,
    
    -- User management
    banned_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE,
    
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- ==================================================
-- USER-ORGANIZATION MEMBERSHIPS - Simple relationship
-- ==================================================
CREATE TABLE users_organizations (
    user_id UUID NOT NULL,
    organization_id UUID NOT NULL,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    invited_by UUID,
    
    PRIMARY KEY (user_id, organization_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE SET NULL
);

-- ==================================================
-- PERMISSIONS - Permission-based authorization system
-- ==================================================
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,

    -- Scope defines WHERE the permission applies (in priority order)
    -- "system" - System-wide permissions for administrators
    -- "org:{organization_id}" - Permissions within a specific organization
    -- "resource" - Permissions on resources regardless of organization
    -- "owner" - Permissions on resources owned by the user
    scope VARCHAR(255) NOT NULL,

    -- Resource type defines WHAT type of resource can be accessed
    -- Specific types: "product", "license", "machine", "policy", "user", "organization"
    -- Wildcard: "*" for all resource types
    resource_type VARCHAR(50) NOT NULL,

    -- Actions define HOW/what operations can be performed
    -- Stored as JSON array: ["create", "read", "update", "delete", "validate", "download"]
    -- Wildcard: ["*"] for all actions
    actions JSONB NOT NULL,

    -- Attributes define WHICH specific resources (empty array = all resources)
    -- Stored as JSON array of resource IDs: ["uuid1", "uuid2"] or [] for all
    attributes JSONB NOT NULL DEFAULT '[]',

    -- Permission metadata
    granted_by UUID,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,

    -- Prevent duplicate permissions for same user/scope/resource_type combination
    UNIQUE(user_id, scope, resource_type),

    -- Validate scope format (system > org > resource > owner priority)
    CONSTRAINT valid_scope CHECK (
        scope = 'system' OR
        scope = 'resource' OR
        scope = 'owner' OR
        (scope ~ '^org:[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$' AND validate_org_scope(scope))
    ),

    -- Validate resource type
    CONSTRAINT valid_resource_type CHECK (
        resource_type IN ('*', 'organization', 'product', 'policy', 'license', 'machine', 'user', 'api_token', 'session')
    ),

    -- Validate actions is a non-empty JSON array
    CONSTRAINT valid_actions CHECK (
        actions IS NOT NULL AND
        jsonb_typeof(actions) = 'array' AND
        jsonb_array_length(actions) > 0
    ),

    -- Validate attributes is a JSON array (can be empty)
    CONSTRAINT valid_attributes CHECK (
        attributes IS NOT NULL AND
        jsonb_typeof(attributes) = 'array'
    ),

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
);

-- ==================================================
-- PERMISSION INDEXES - Optimized for frequent lookups
-- ==================================================

-- Primary lookup index: user_id + resource_type (most common query pattern)
CREATE INDEX idx_permissions_user_resource ON permissions(user_id, resource_type);

-- Secondary lookup index: user_id + scope (for scope-based filtering)
CREATE INDEX idx_permissions_user_scope ON permissions(user_id, scope);

-- Composite index for full permission checks: user_id + scope + resource_type
CREATE INDEX idx_permissions_user_scope_resource ON permissions(user_id, scope, resource_type);

-- Index for permission management queries by granted_by
CREATE INDEX idx_permissions_granted_by ON permissions(granted_by) WHERE granted_by IS NOT NULL;

-- Index for expiration cleanup queries
CREATE INDEX idx_permissions_expires_at ON permissions(expires_at) WHERE expires_at IS NOT NULL;

-- Partial index for system-level permissions (high-privilege operations)
CREATE INDEX idx_permissions_system_scope ON permissions(user_id, resource_type) WHERE scope = 'system';

-- Partial index for organization-scoped permissions (most common in multi-tenant)
CREATE INDEX idx_permissions_org_scope ON permissions(user_id, resource_type, scope) WHERE scope LIKE 'org:%';

-- GIN index for efficient JSON array searches on actions
CREATE INDEX idx_permissions_actions_gin ON permissions USING GIN(actions);

-- GIN index for efficient JSON array searches on attributes
CREATE INDEX idx_permissions_attributes_gin ON permissions USING GIN(attributes);

-- ==================================================
-- PERMISSION TRIGGERS - Maintain referential integrity
-- ==================================================

-- Trigger function to clean up permissions when organizations are deleted
CREATE OR REPLACE FUNCTION cleanup_org_permissions()
RETURNS TRIGGER AS $$
BEGIN
    -- Remove permissions scoped to the deleted organization
    DELETE FROM permissions
    WHERE scope = 'org:' || OLD.id::text;

    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Trigger to clean up permissions when organizations are soft-deleted
CREATE TRIGGER trigger_cleanup_org_permissions
    AFTER UPDATE OF deleted_at ON organizations
    FOR EACH ROW
    WHEN (OLD.deleted_at IS NULL AND NEW.deleted_at IS NOT NULL)
    EXECUTE FUNCTION cleanup_org_permissions();

-- Trigger to clean up permissions when organizations are hard-deleted
CREATE TRIGGER trigger_cleanup_org_permissions_delete
    AFTER DELETE ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION cleanup_org_permissions();

-- ==================================================
-- PRODUCTS - Software products from vendors
-- ==================================================
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL,
    description TEXT,
    url VARCHAR(512),
    
    -- Platform and distribution
    platforms JSONB DEFAULT '[]',
    distribution_strategy VARCHAR(255),
    
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    UNIQUE (organization_id, code)
);

-- ==================================================
-- POLICIES - FULL License behavior configuration
-- ==================================================
CREATE TABLE policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    product_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    
    -- Basic configuration
    strict BOOLEAN DEFAULT FALSE,
    protected BOOLEAN DEFAULT FALSE,
    duration INTEGER, -- in seconds
    lock_version INTEGER DEFAULT 0,
    
    -- License behavior
    floating BOOLEAN DEFAULT FALSE, -- License transferability
    use_pool BOOLEAN DEFAULT FALSE, -- License pooling
    encrypted BOOLEAN DEFAULT FALSE, -- License encryption
    
    -- Cryptographic scheme
    scheme VARCHAR(50) DEFAULT 'ED25519_SIGN' CHECK (scheme IN (
        'LEGACY_ENCRYPT', 'RSA_2048_PKCS1_ENCRYPT', 'RSA_2048_PKCS1_SIGN',
        'RSA_2048_PKCS1_PSS_SIGN', 'RSA_2048_JWT_RS256', 'ED25519_SIGN', 'ED25519_JWT_ES256'
    )),
    
    -- Limits and constraints
    max_machines INTEGER,
    max_uses INTEGER,
    max_cores INTEGER,
    max_users INTEGER,
    max_processes INTEGER,
    
    -- Heartbeat configuration
    require_heartbeat BOOLEAN DEFAULT FALSE,
    heartbeat_duration INTEGER, -- in seconds
    heartbeat_basis VARCHAR(255),
    heartbeat_cull_strategy VARCHAR(255),
    heartbeat_resurrection_strategy VARCHAR(255),
    
    -- Check-in configuration
    require_check_in BOOLEAN DEFAULT FALSE,
    check_in_interval VARCHAR(255),
    check_in_interval_count INTEGER,
    
    -- Strategy configurations
    machine_uniqueness_strategy VARCHAR(255),
    machine_matching_strategy VARCHAR(255),
    machine_leasing_strategy VARCHAR(255),
    component_uniqueness_strategy VARCHAR(255),
    component_matching_strategy VARCHAR(255),
    process_leasing_strategy VARCHAR(255),
    expiration_strategy VARCHAR(255),
    expiration_basis VARCHAR(255),
    renewal_basis VARCHAR(255),
    authentication_strategy VARCHAR(255),
    transfer_strategy VARCHAR(255),
    overage_strategy VARCHAR(255),
    
    -- Scope requirements
    require_product_scope BOOLEAN DEFAULT FALSE,
    require_policy_scope BOOLEAN DEFAULT FALSE,
    require_machine_scope BOOLEAN DEFAULT FALSE,
    require_fingerprint_scope BOOLEAN DEFAULT FALSE,
    require_user_scope BOOLEAN DEFAULT FALSE,
    require_checksum_scope BOOLEAN DEFAULT FALSE,
    require_version_scope BOOLEAN DEFAULT FALSE,
    require_components_scope BOOLEAN DEFAULT FALSE,
    
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- ==================================================
-- LICENSES - FULL License tracking
-- ==================================================
CREATE TABLE licenses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    product_id UUID NOT NULL,
    policy_id UUID NOT NULL,
    key VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    
    -- License ownership (polymorphic)
    owner_type VARCHAR(50) NOT NULL CHECK (owner_type IN ('user', 'organization')),
    owner_id UUID NOT NULL,
    
    -- License state
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'suspended', 'banned')),
    suspended BOOLEAN DEFAULT FALSE,
    protected BOOLEAN DEFAULT FALSE,
    
    -- Usage tracking
    uses INTEGER DEFAULT 0,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used TIMESTAMP WITH TIME ZONE,
    
    -- Policy overrides (allow per-license customization)
    max_uses_override INTEGER,
    max_machines_override INTEGER,
    max_cores_override INTEGER,
    max_users_override INTEGER,
    max_processes_override INTEGER,
    
    -- Cached counts for performance
    machines_count INTEGER DEFAULT 0,
    machines_core_count INTEGER DEFAULT 0,
    license_users_count INTEGER DEFAULT 0,
    
    -- Heartbeat and validation tracking
    last_check_in_at TIMESTAMP WITH TIME ZONE,
    last_validated_at TIMESTAMP WITH TIME ZONE,
    last_validated_checksum VARCHAR(255),
    last_validated_version VARCHAR(255),
    last_check_out_at TIMESTAMP WITH TIME ZONE,
    
    -- Event tracking (for notification management)
    last_expiration_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_check_in_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_expiring_soon_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_check_in_soon_event_sent_at TIMESTAMP WITH TIME ZONE,
    
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (policy_id) REFERENCES policies(id) ON DELETE CASCADE,
    UNIQUE (organization_id, key)
);

-- ==================================================
-- MACHINES - FULL Machine tracking
-- ==================================================
CREATE TABLE machines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    license_id UUID NOT NULL,
    policy_id UUID NOT NULL,
    owner_id UUID, -- User who owns this machine
    
    -- Machine identification
    fingerprint VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    hostname VARCHAR(255),
    platform VARCHAR(255),
    
    -- Network information
    ip VARCHAR(45), -- IPv4/IPv6
    
    -- Machine state
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    activated_at TIMESTAMP WITH TIME ZONE,
    deactivated_at TIMESTAMP WITH TIME ZONE,
    last_seen TIMESTAMP WITH TIME ZONE,
    
    -- Hardware tracking
    cores INTEGER DEFAULT 0,
    
    -- Component fingerprinting
    components JSONB DEFAULT '{}',
    
    -- Heartbeat tracking
    last_heartbeat_at TIMESTAMP WITH TIME ZONE,
    next_heartbeat_at TIMESTAMP WITH TIME ZONE,
    last_death_event_sent_at TIMESTAMP WITH TIME ZONE,
    heartbeat_jid VARCHAR(255), -- Job ID for heartbeat processing
    
    -- Check-out tracking (for floating licenses)
    last_check_out_at TIMESTAMP WITH TIME ZONE,

    -- Process override (per-machine limit)
    max_processes_override INTEGER,

    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
    FOREIGN KEY (policy_id) REFERENCES policies(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE (license_id, fingerprint)
);

-- ==================================================
-- SESSIONS - User authentication sessions
-- ==================================================
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    
    -- Session details
    ip VARCHAR(45),
    user_agent TEXT,
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- ==================================================
-- API TOKENS - API access tokens
-- ==================================================
CREATE TABLE api_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    organization_id UUID,
    name VARCHAR(255) NOT NULL,
    
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    permissions TEXT[], -- Array of permissions for OPA
    
    active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);

-- ==================================================
-- COMPREHENSIVE INDEXES - Optimized for permission-based queries
-- ==================================================

-- Organizations indexes
CREATE INDEX idx_organizations_slug ON organizations(slug) WHERE deleted_at IS NULL;
CREATE INDEX idx_organizations_email ON organizations(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_organizations_type_status ON organizations(type, status) WHERE deleted_at IS NULL;

-- Users indexes
CREATE INDEX idx_users_email ON users(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_status ON users(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_last_login ON users(last_login) WHERE deleted_at IS NULL;

-- User-Organization membership indexes
CREATE INDEX idx_users_organizations_user ON users_organizations(user_id);
CREATE INDEX idx_users_organizations_org ON users_organizations(organization_id);
CREATE INDEX idx_users_organizations_joined ON users_organizations(joined_at);

-- Products indexes
CREATE INDEX idx_products_organization ON products(organization_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_products_code ON products(organization_id, code) WHERE deleted_at IS NULL;
CREATE INDEX idx_products_name ON products(name) WHERE deleted_at IS NULL;

-- Policies indexes
CREATE INDEX idx_policies_organization ON policies(organization_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_policies_product ON policies(product_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_policies_name ON policies(name) WHERE deleted_at IS NULL;

-- Licenses indexes (critical for permission checks)
CREATE INDEX idx_licenses_organization ON licenses(organization_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_licenses_product ON licenses(product_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_licenses_policy ON licenses(policy_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_licenses_key ON licenses(organization_id, key) WHERE deleted_at IS NULL;
CREATE INDEX idx_licenses_owner ON licenses(owner_type, owner_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_licenses_status ON licenses(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_licenses_expires_at ON licenses(expires_at) WHERE deleted_at IS NULL AND expires_at IS NOT NULL;

-- Machines indexes (critical for permission checks)
CREATE INDEX idx_machines_license ON machines(license_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_machines_policy ON machines(policy_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_machines_owner ON machines(owner_id) WHERE deleted_at IS NULL AND owner_id IS NOT NULL;
CREATE INDEX idx_machines_fingerprint ON machines(license_id, fingerprint) WHERE deleted_at IS NULL;
CREATE INDEX idx_machines_status ON machines(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_machines_last_seen ON machines(last_seen) WHERE deleted_at IS NULL;

-- Sessions indexes
CREATE INDEX idx_sessions_user ON sessions(user_id);
CREATE INDEX idx_sessions_token_hash ON sessions(token_hash);
CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);

-- API Tokens indexes
CREATE INDEX idx_api_tokens_user ON api_tokens(user_id);
CREATE INDEX idx_api_tokens_organization ON api_tokens(organization_id);
CREATE INDEX idx_api_tokens_active ON api_tokens(active);
CREATE INDEX idx_api_tokens_expires_at ON api_tokens(expires_at) WHERE expires_at IS NOT NULL;

-- ==================================================
-- PERMISSION SYSTEM DOCUMENTATION
-- ==================================================

/*
PERMISSION-BASED AUTHORIZATION SYSTEM

This schema implements a flexible, hierarchical permission system with the following components:

1. SCOPE HIERARCHY (evaluated in priority order):
   - "system": System-wide permissions for administrators
   - "org:{uuid}": Organization-scoped permissions
   - "resource": Resource-level permissions regardless of organization
   - "owner": Permissions on resources owned by the user

2. RESOURCE TYPES:
   - "*": All resource types (wildcard)
   - "organization": Organization management
   - "product": Product management
   - "policy": Policy management
   - "license": License management
   - "machine": Machine management
   - "user": User management
   - "api_token": API token management
   - "session": Session management

3. ACTIONS (stored as JSON array):
   - ["*"]: All actions (wildcard)
   - ["create", "read", "update", "delete"]: Standard CRUD operations
   - ["validate", "download"]: Special operations for licenses/products

4. ATTRIBUTES (stored as JSON array):
   - []: Empty array means permission applies to all resources of the type
   - ["uuid1", "uuid2"]: Specific resource IDs the permission applies to

5. PERMISSION EVALUATION:
   - Permissions are evaluated in scope priority order (system > org > resource > owner)
   - First matching permission with required action grants access
   - No matching permission denies access
   - Wildcard actions ("*") grant all actions
   - Empty attributes array grants access to all resources of the type

6. PERFORMANCE OPTIMIZATIONS:
   - Composite indexes for common query patterns (user_id + resource_type)
   - Partial indexes for high-privilege operations (system scope)
   - GIN indexes for efficient JSON array searches
   - Referential integrity maintained via triggers

EXAMPLE PERMISSION RECORDS:

-- System admin with all permissions
INSERT INTO permissions (user_id, scope, resource_type, actions, attributes)
VALUES ('admin-uuid', 'system', '*', '["*"]', '[]');

-- Organization admin for specific org
INSERT INTO permissions (user_id, scope, resource_type, actions, attributes)
VALUES ('user-uuid', 'org:org-uuid', '*', '["*"]', '[]');

-- License manager for specific licenses
INSERT INTO permissions (user_id, scope, resource_type, actions, attributes)
VALUES ('user-uuid', 'resource', 'license', '["read", "update", "validate"]', '["license-uuid-1", "license-uuid-2"]');

-- Owner permissions (user can manage their own resources)
INSERT INTO permissions (user_id, scope, resource_type, actions, attributes)
VALUES ('user-uuid', 'owner', 'machine', '["read", "update", "delete"]', '[]');
*/
