-- Optimized core database tables for gokeys license management system
-- Simplified schema with full cryptographic support and OPA compatibility
-- Created: 2025-07-23

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ==================================================
-- ORGANIZATIONS - Vendors, Resellers, Enterprise Customers
-- ==================================================
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    
    -- Organization type
    type VARCHAR(50) DEFAULT 'vendor' CHECK (type IN ('vendor', 'reseller', 'customer')),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'canceled')),
    protected BOOLEAN DEFAULT FALSE,
    
    -- Cryptographic keys (encrypted at rest) - FULL SUPPORT
    public_key TEXT, -- RSA public key
    private_key TEXT, -- RSA private key (encrypted)
    secret_key TEXT, -- 128-char hex secret
    ed25519_private_key TEXT, -- Ed25519 private key (encrypted)
    ed25519_public_key TEXT, -- Ed25519 public key
    
    -- Resource limits
    max_users INTEGER,
    max_licenses INTEGER,
    max_machines INTEGER,
    
    -- Configuration
    settings JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- ==================================================
-- USERS - Independent users (no forced organization)
-- ==================================================
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    
    -- Simple status
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    
    -- 2FA support
    totp_secret TEXT,
    totp_enabled BOOLEAN DEFAULT FALSE,
    
    -- Password reset
    password_reset_token VARCHAR(255),
    password_reset_expires_at TIMESTAMP WITH TIME ZONE,
    
    -- User management
    banned_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE,
    
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- ==================================================
-- USER-ORGANIZATION MEMBERSHIPS - Simple relationship
-- ==================================================
CREATE TABLE users_organizations (
    user_id UUID NOT NULL,
    organization_id UUID NOT NULL,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    invited_by UUID,
    
    PRIMARY KEY (user_id, organization_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE SET NULL
);

-- ==================================================
-- PERMISSIONS - Pure resource-based permissions
-- ==================================================
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    
    -- Scope defines WHERE the permission applies
    scope VARCHAR(255) NOT NULL, -- "system", "org:xxx", "resource:type:id", "owner"
    
    -- Resource type defines WHAT type of resource
    resource_type VARCHAR(50) NOT NULL, -- "product", "license", "user", "*"
    
    -- Actions define WHAT can be done
    actions TEXT[] NOT NULL, -- ["create", "read", "update", "delete"] or ["*"]
    
    -- Metadata
    granted_by UUID,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Prevent duplicate permissions
    UNIQUE(user_id, scope, resource_type),
    
    -- Validate scope format
    CONSTRAINT valid_scope CHECK (
        scope = 'system' OR
        scope = 'owner' OR
        scope LIKE 'org:%' OR
        scope LIKE 'resource:%:%'
    ),
    
    -- Validate resource type
    CONSTRAINT valid_resource_type CHECK (
        resource_type IN ('*', 'organization', 'product', 'policy', 'license', 'machine', 'user', 'api_token', 'session')
    ),
    
    -- Validate actions
    CONSTRAINT valid_actions CHECK (
        actions IS NOT NULL AND 
        array_length(actions, 1) > 0
    ),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
);

-- ==================================================
-- PRODUCTS - Software products from vendors
-- ==================================================
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL,
    description TEXT,
    url VARCHAR(512),
    
    -- Platform and distribution
    platforms JSONB DEFAULT '[]',
    distribution_strategy VARCHAR(255),
    
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    UNIQUE (organization_id, code)
);

-- ==================================================
-- POLICIES - FULL License behavior configuration
-- ==================================================
CREATE TABLE policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    product_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    
    -- Basic configuration
    strict BOOLEAN DEFAULT FALSE,
    protected BOOLEAN DEFAULT FALSE,
    duration INTEGER, -- in seconds
    lock_version INTEGER DEFAULT 0,
    
    -- License behavior
    floating BOOLEAN DEFAULT FALSE, -- License transferability
    use_pool BOOLEAN DEFAULT FALSE, -- License pooling
    encrypted BOOLEAN DEFAULT FALSE, -- License encryption
    
    -- Cryptographic scheme
    scheme VARCHAR(50) DEFAULT 'ED25519_SIGN' CHECK (scheme IN (
        'LEGACY_ENCRYPT', 'RSA_2048_PKCS1_ENCRYPT', 'RSA_2048_PKCS1_SIGN',
        'RSA_2048_PKCS1_PSS_SIGN', 'RSA_2048_JWT_RS256', 'ED25519_SIGN', 'ED25519_JWT_ES256'
    )),
    
    -- Limits and constraints
    max_machines INTEGER,
    max_uses INTEGER,
    max_cores INTEGER,
    max_users INTEGER,
    max_processes INTEGER,
    
    -- Heartbeat configuration
    require_heartbeat BOOLEAN DEFAULT FALSE,
    heartbeat_duration INTEGER, -- in seconds
    heartbeat_basis VARCHAR(255),
    heartbeat_cull_strategy VARCHAR(255),
    heartbeat_resurrection_strategy VARCHAR(255),
    
    -- Check-in configuration
    require_check_in BOOLEAN DEFAULT FALSE,
    check_in_interval VARCHAR(255),
    check_in_interval_count INTEGER,
    
    -- Strategy configurations
    machine_uniqueness_strategy VARCHAR(255),
    machine_matching_strategy VARCHAR(255),
    machine_leasing_strategy VARCHAR(255),
    component_uniqueness_strategy VARCHAR(255),
    component_matching_strategy VARCHAR(255),
    process_leasing_strategy VARCHAR(255),
    expiration_strategy VARCHAR(255),
    expiration_basis VARCHAR(255),
    renewal_basis VARCHAR(255),
    authentication_strategy VARCHAR(255),
    transfer_strategy VARCHAR(255),
    overage_strategy VARCHAR(255),
    
    -- Scope requirements
    require_product_scope BOOLEAN DEFAULT FALSE,
    require_policy_scope BOOLEAN DEFAULT FALSE,
    require_machine_scope BOOLEAN DEFAULT FALSE,
    require_fingerprint_scope BOOLEAN DEFAULT FALSE,
    require_user_scope BOOLEAN DEFAULT FALSE,
    require_checksum_scope BOOLEAN DEFAULT FALSE,
    require_version_scope BOOLEAN DEFAULT FALSE,
    require_components_scope BOOLEAN DEFAULT FALSE,
    
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- ==================================================
-- LICENSES - FULL License tracking
-- ==================================================
CREATE TABLE licenses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    product_id UUID NOT NULL,
    policy_id UUID NOT NULL,
    key VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    
    -- License ownership (polymorphic)
    owner_type VARCHAR(50) NOT NULL CHECK (owner_type IN ('user', 'organization')),
    owner_id UUID NOT NULL,
    
    -- License state
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'suspended', 'banned')),
    suspended BOOLEAN DEFAULT FALSE,
    protected BOOLEAN DEFAULT FALSE,
    
    -- Usage tracking
    uses INTEGER DEFAULT 0,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used TIMESTAMP WITH TIME ZONE,
    
    -- Policy overrides (allow per-license customization)
    max_uses_override INTEGER,
    max_machines_override INTEGER,
    max_cores_override INTEGER,
    max_users_override INTEGER,
    max_processes_override INTEGER,
    
    -- Cached counts for performance
    machines_count INTEGER DEFAULT 0,
    machines_core_count INTEGER DEFAULT 0,
    license_users_count INTEGER DEFAULT 0,
    
    -- Heartbeat and validation tracking
    last_check_in_at TIMESTAMP WITH TIME ZONE,
    last_validated_at TIMESTAMP WITH TIME ZONE,
    last_validated_checksum VARCHAR(255),
    last_validated_version VARCHAR(255),
    last_check_out_at TIMESTAMP WITH TIME ZONE,
    
    -- Event tracking (for notification management)
    last_expiration_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_check_in_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_expiring_soon_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_check_in_soon_event_sent_at TIMESTAMP WITH TIME ZONE,
    
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (policy_id) REFERENCES policies(id) ON DELETE CASCADE,
    UNIQUE (organization_id, key)
);

-- ==================================================
-- MACHINES - FULL Machine tracking
-- ==================================================
CREATE TABLE machines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    license_id UUID NOT NULL,
    policy_id UUID NOT NULL,
    owner_id UUID, -- User who owns this machine
    
    -- Machine identification
    fingerprint VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    hostname VARCHAR(255),
    platform VARCHAR(255),
    
    -- Network information
    ip VARCHAR(45), -- IPv4/IPv6
    
    -- Machine state
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    activated_at TIMESTAMP WITH TIME ZONE,
    deactivated_at TIMESTAMP WITH TIME ZONE,
    last_seen TIMESTAMP WITH TIME ZONE,
    
    -- Hardware tracking
    cores INTEGER DEFAULT 0,
    
    -- Component fingerprinting
    components JSONB DEFAULT '{}',
    
    -- Heartbeat tracking
    last_heartbeat_at TIMESTAMP WITH TIME ZONE,
    next_heartbeat_at TIMESTAMP WITH TIME ZONE,
    last_death_event_sent_at TIMESTAMP WITH TIME ZONE,
    heartbeat_jid VARCHAR(255), -- Job ID for heartbeat processing
    
    -- Check-out tracking (for floating licenses)
    last_check_out_at TIMESTAMP WITH TIME ZONE,

    -- Process override (per-machine limit)
    max_processes_override INTEGER,

    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
    FOREIGN KEY (policy_id) REFERENCES policies(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE (license_id, fingerprint)
);

-- ==================================================
-- SESSIONS - User authentication sessions
-- ==================================================
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    
    -- Session details
    ip VARCHAR(45),
    user_agent TEXT,
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- ==================================================
-- API TOKENS - API access tokens
-- ==================================================
CREATE TABLE api_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    organization_id UUID,
    name VARCHAR(255) NOT NULL,
    
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    permissions TEXT[], -- Array of permissions for OPA
    
    active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);
