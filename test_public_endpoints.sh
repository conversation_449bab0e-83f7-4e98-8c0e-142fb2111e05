#!/bin/bash

# Test Public API Endpoints (No Authentication Required)
# These endpoints are typically used by client applications

API_BASE="http://localhost:8080/api/v1"
HEALTH_URL="http://localhost:8080/health"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}GoKeys Public API Endpoints Test${NC}"
echo "=================================="

# Helper function to test endpoint
test_public_endpoint() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo -e "${GREEN}Testing: $description${NC}"
    echo "URL: $method $url"
    
    if [ -n "$data" ]; then
        echo "Data: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$url")
    fi
    
    # Extract HTTP status code and body
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "Status: $http_code"
    echo "Response:"
    echo "$body" | jq '.' 2>/dev/null || echo "$body"
    
    if [[ $http_code =~ ^2[0-9][0-9]$ ]]; then
        echo -e "${GREEN}✓ Success${NC}"
    else
        echo -e "${RED}✗ Failed${NC}"
    fi
    echo "----------------------------------------"
}

# 1. Health Check
test_public_endpoint "GET" "$HEALTH_URL" "" "Health Check"

# 2. Root endpoint
test_public_endpoint "GET" "http://localhost:8080/" "" "Root Endpoint"

# 3. License Validation (Public endpoint)
echo -e "${YELLOW}Note: License validation requires a valid license ID${NC}"
echo "Example license validation test:"
echo "curl -X POST $API_BASE/licenses/YOUR_LICENSE_ID/actions/validate"
echo ""

# 4. Machine Heartbeat (Public endpoint)
echo -e "${YELLOW}Note: Machine heartbeat requires a valid machine ID${NC}"
echo "Example machine heartbeat test:"
echo "curl -X POST $API_BASE/machines/YOUR_MACHINE_ID/actions/heartbeat"
echo ""

# 5. Test with sample IDs (these will likely fail but show the endpoint structure)
echo -e "${BLUE}Testing with sample IDs (expected to fail):${NC}"

# Sample license validation
test_public_endpoint "POST" "$API_BASE/licenses/sample-license-id/actions/validate" '{}' "License Validation (Sample ID)"

# Sample machine heartbeat
test_public_endpoint "POST" "$API_BASE/machines/sample-machine-id/actions/heartbeat" '{}' "Machine Heartbeat (Sample ID)"

echo ""
echo "=================================="
echo -e "${GREEN}Public endpoints test completed!${NC}"
echo ""
echo -e "${YELLOW}Tips:${NC}"
echo "1. Use the main test_api.sh script to create real resources first"
echo "2. Then use the returned IDs to test license validation and machine heartbeat"
echo "3. Health check should always return 200 OK when server is running"
