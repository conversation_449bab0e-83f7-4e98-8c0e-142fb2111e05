-- Migration to optimize permissions table with flattened permission keys
-- This migration transforms the existing permission structure to use flattened keys for O(1) lookups

BEGIN;

-- Create new optimized permissions table
CREATE TABLE permissions_optimized (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    permission_key VARCHAR(255) NOT NULL, -- Format: "{scope}:{resource_type}:{action}"
    resource_ids JSONB DEFAULT '[]'::jsonb, -- [] = wildcard, ["id1", "id2"] = specific resources
    granted_by UUID,
    granted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT fk_permissions_optimized_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_permissions_optimized_granter FOREIG<PERSON> KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create optimized indexes for ultra-fast lookups
CREATE UNIQUE INDEX idx_permissions_optimized_user_key ON permissions_optimized(user_id, permission_key);
CREATE INDEX idx_permissions_optimized_lookup ON permissions_optimized(user_id, permission_key) WHERE expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP;
CREATE INDEX idx_permissions_optimized_key_pattern ON permissions_optimized USING gin(permission_key gin_trgm_ops);
CREATE INDEX idx_permissions_optimized_expires ON permissions_optimized(expires_at) WHERE expires_at IS NOT NULL;

-- Function to convert legacy permission to flattened key
CREATE OR REPLACE FUNCTION build_permission_key(scope_val TEXT, resource_type_val TEXT, action_val TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN scope_val || ':' || resource_type_val || ':' || action_val;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Migrate existing permissions to optimized format
-- This handles the complex transformation from array-based actions to individual permission keys
INSERT INTO permissions_optimized (id, user_id, permission_key, resource_ids, granted_by, granted_at, expires_at)
SELECT 
    gen_random_uuid() as id,
    p.user_id,
    build_permission_key(p.scope, p.resource_type, action_item) as permission_key,
    '[]'::jsonb as resource_ids, -- Start with wildcard, can be refined later
    p.granted_by,
    p.granted_at,
    p.expires_at
FROM permissions p
CROSS JOIN LATERAL unnest(p.actions) as action_item
WHERE p.expires_at IS NULL OR p.expires_at > CURRENT_TIMESTAMP;

-- Create view for backward compatibility
CREATE VIEW permissions_legacy AS
SELECT 
    id,
    user_id,
    split_part(permission_key, ':', 1) as scope,
    split_part(permission_key, ':', 2) as resource_type,
    ARRAY[split_part(permission_key, ':', 3)] as actions,
    granted_by,
    granted_at,
    expires_at
FROM permissions_optimized;

-- Rename tables to complete migration
ALTER TABLE permissions RENAME TO permissions_old;
ALTER TABLE permissions_optimized RENAME TO permissions;

-- Update sequences and constraints
ALTER TABLE permissions RENAME CONSTRAINT fk_permissions_optimized_user TO fk_permissions_user;
ALTER TABLE permissions RENAME CONSTRAINT fk_permissions_optimized_granter TO fk_permissions_granter;

-- Rename indexes
ALTER INDEX idx_permissions_optimized_user_key RENAME TO idx_permissions_user_key;
ALTER INDEX idx_permissions_optimized_lookup RENAME TO idx_permissions_lookup;
ALTER INDEX idx_permissions_optimized_key_pattern RENAME TO idx_permissions_key_pattern;
ALTER INDEX idx_permissions_optimized_expires RENAME TO idx_permissions_expires;

-- Create additional performance indexes
CREATE INDEX idx_permissions_system_scope ON permissions(user_id) WHERE permission_key LIKE 'system:%';
CREATE INDEX idx_permissions_org_scope ON permissions(user_id, permission_key) WHERE permission_key LIKE 'org:%';
CREATE INDEX idx_permissions_resource_scope ON permissions(user_id, permission_key) WHERE permission_key LIKE 'resource:%';
CREATE INDEX idx_permissions_owner_scope ON permissions(user_id, permission_key) WHERE permission_key LIKE 'owner:%';

-- Create function for efficient permission checking
CREATE OR REPLACE FUNCTION check_user_permission(
    p_user_id UUID,
    p_scope TEXT,
    p_resource_type TEXT,
    p_action TEXT,
    p_resource_id TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    permission_keys TEXT[];
    has_permission BOOLEAN := FALSE;
BEGIN
    -- Build hierarchical permission keys to check
    permission_keys := ARRAY[
        -- System permissions (highest priority)
        'system:*:*',
        'system:' || p_resource_type || ':*',
        'system:*:' || p_action,
        'system:' || p_resource_type || ':' || p_action
    ];
    
    -- Add organization permissions if scope is org-based
    IF p_scope LIKE 'org:%' THEN
        permission_keys := permission_keys || ARRAY[
            p_scope || ':*:*',
            p_scope || ':' || p_resource_type || ':*',
            p_scope || ':*:' || p_action,
            p_scope || ':' || p_resource_type || ':' || p_action
        ];
    END IF;
    
    -- Add resource permissions
    permission_keys := permission_keys || ARRAY[
        'resource:*:*',
        'resource:' || p_resource_type || ':*',
        'resource:*:' || p_action,
        'resource:' || p_resource_type || ':' || p_action
    ];
    
    -- Add owner permissions
    permission_keys := permission_keys || ARRAY[
        'owner:*:*',
        'owner:' || p_resource_type || ':*',
        'owner:*:' || p_action,
        'owner:' || p_resource_type || ':' || p_action
    ];
    
    -- Check if user has any of these permissions
    SELECT EXISTS(
        SELECT 1 FROM permissions 
        WHERE user_id = p_user_id 
        AND permission_key = ANY(permission_keys)
        AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
        AND (
            p_resource_id IS NULL 
            OR resource_ids = '[]'::jsonb 
            OR resource_ids ? p_resource_id
        )
    ) INTO has_permission;
    
    RETURN has_permission;
END;
$$ LANGUAGE plpgsql STABLE;

-- Create function for batch permission checking
CREATE OR REPLACE FUNCTION batch_check_user_permissions(
    p_user_id UUID,
    p_permission_checks JSONB -- Array of {scope, resource_type, action, resource_id}
)
RETURNS JSONB AS $$
DECLARE
    check_item JSONB;
    result JSONB := '[]'::jsonb;
    has_permission BOOLEAN;
BEGIN
    FOR check_item IN SELECT jsonb_array_elements(p_permission_checks)
    LOOP
        SELECT check_user_permission(
            p_user_id,
            check_item->>'scope',
            check_item->>'resource_type',
            check_item->>'action',
            check_item->>'resource_id'
        ) INTO has_permission;
        
        result := result || jsonb_build_object(
            'scope', check_item->>'scope',
            'resource_type', check_item->>'resource_type',
            'action', check_item->>'action',
            'resource_id', check_item->>'resource_id',
            'allowed', has_permission
        );
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql STABLE;

-- Create materialized view for permission analytics
CREATE MATERIALIZED VIEW permission_analytics AS
SELECT 
    split_part(permission_key, ':', 1) as scope_type,
    split_part(permission_key, ':', 2) as resource_type,
    split_part(permission_key, ':', 3) as action,
    COUNT(*) as permission_count,
    COUNT(DISTINCT user_id) as user_count
FROM permissions
WHERE expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP
GROUP BY split_part(permission_key, ':', 1), split_part(permission_key, ':', 2), split_part(permission_key, ':', 3);

CREATE UNIQUE INDEX idx_permission_analytics ON permission_analytics(scope_type, resource_type, action);

-- Create trigger to refresh analytics on permission changes
CREATE OR REPLACE FUNCTION refresh_permission_analytics()
RETURNS TRIGGER AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY permission_analytics;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_refresh_permission_analytics
    AFTER INSERT OR UPDATE OR DELETE ON permissions
    FOR EACH STATEMENT
    EXECUTE FUNCTION refresh_permission_analytics();

-- Add comments for documentation
COMMENT ON TABLE permissions IS 'Optimized permissions table using flattened permission keys for O(1) lookups';
COMMENT ON COLUMN permissions.permission_key IS 'Flattened permission key format: {scope}:{resource_type}:{action}';
COMMENT ON COLUMN permissions.resource_ids IS 'JSONB array of specific resource IDs. Empty array = wildcard access';
COMMENT ON FUNCTION check_user_permission IS 'Efficient function to check if user has specific permission';
COMMENT ON FUNCTION batch_check_user_permissions IS 'Batch permission checking for multiple permissions at once';

COMMIT;
