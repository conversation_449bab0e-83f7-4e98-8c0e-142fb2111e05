-- Rollback migration for optimized permissions
-- This restores the original permission structure

BEGIN;

-- Drop triggers and functions
DROP TRIGGER IF EXISTS trigger_refresh_permission_analytics ON permissions;
DROP FUNCTION IF EXISTS refresh_permission_analytics();
DROP MATERIALIZED VIEW IF EXISTS permission_analytics;
DROP FUNCTION IF EXISTS batch_check_user_permissions(UUID, JSONB);
DROP FUNCTION IF EXISTS check_user_permission(UUID, TEXT, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS build_permission_key(TEXT, TEXT, TEXT);

-- Drop optimized indexes
DROP INDEX IF EXISTS idx_permissions_owner_scope;
DROP INDEX IF EXISTS idx_permissions_resource_scope;
DROP INDEX IF EXISTS idx_permissions_org_scope;
DROP INDEX IF EXISTS idx_permissions_system_scope;
DROP INDEX IF EXISTS idx_permissions_expires;
DROP INDEX IF EXISTS idx_permissions_key_pattern;
DROP INDEX IF EXISTS idx_permissions_lookup;
DROP INDEX IF EXISTS idx_permissions_user_key;

-- Restore original permissions table structure
CREATE TABLE permissions_restored (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    scope VARCHAR(255) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    actions TEXT[] NOT NULL,
    granted_by UUID,
    granted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT fk_permissions_restored_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_permissions_restored_granter FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Migrate data back to original format
-- Aggregate flattened permissions back into array-based actions
INSERT INTO permissions_restored (user_id, scope, resource_type, actions, granted_by, granted_at, expires_at)
SELECT 
    user_id,
    split_part(permission_key, ':', 1) as scope,
    split_part(permission_key, ':', 2) as resource_type,
    array_agg(split_part(permission_key, ':', 3)) as actions,
    granted_by,
    granted_at,
    expires_at
FROM permissions
WHERE expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP
GROUP BY user_id, split_part(permission_key, ':', 1), split_part(permission_key, ':', 2), granted_by, granted_at, expires_at;

-- Drop the legacy view
DROP VIEW IF EXISTS permissions_legacy;

-- Rename tables back
DROP TABLE permissions;
ALTER TABLE permissions_old RENAME TO permissions;
ALTER TABLE permissions_restored RENAME TO permissions_new;
DROP TABLE permissions_new;

-- Restore original indexes
CREATE INDEX idx_permissions_user_scope ON permissions(user_id, scope);
CREATE INDEX idx_permissions_scope_pattern ON permissions(scope);
CREATE INDEX idx_permissions_resource_type ON permissions(resource_type);
CREATE INDEX idx_permissions_expires ON permissions(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_permissions_active ON permissions(user_id) WHERE expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP;

COMMIT;
