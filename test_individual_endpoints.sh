#!/bin/bash

# Individual API Endpoint Tests
# Use this script to test specific endpoints manually

API_BASE="http://localhost:8080/api/v1"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}GoKeys API Individual Endpoint Tests${NC}"
echo "======================================"

# 1. Test Health Check
echo -e "${GREEN}1. Health Check${NC}"
curl -s "$API_BASE/../health" | jq '.' 2>/dev/null || curl -s "$API_BASE/../health"
echo -e "\n"

# 2. Test Organizations
echo -e "${GREEN}2. Organizations${NC}"
echo "Create Organization:"
curl -X POST "$API_BASE/organizations" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Sample Organization",
    "description": "Test organization",
    "website": "https://example.com",
    "email": "<EMAIL>"
  }' | jq '.' 2>/dev/null || echo "Response received"

echo -e "\nList Organizations:"
curl -s "$API_BASE/organizations" | jq '.' 2>/dev/null || curl -s "$API_BASE/organizations"
echo -e "\n"

# 3. Test Products
echo -e "${GREEN}3. Products${NC}"
echo "List Products:"
curl -s "$API_BASE/products" | jq '.' 2>/dev/null || curl -s "$API_BASE/products"
echo -e "\n"

# 4. Test Policies
echo -e "${GREEN}4. Policies${NC}"
echo "List Policies:"
curl -s "$API_BASE/policies" | jq '.' 2>/dev/null || curl -s "$API_BASE/policies"
echo -e "\n"

# 5. Test Licenses
echo -e "${GREEN}5. Licenses${NC}"
echo "List Licenses:"
curl -s "$API_BASE/licenses" | jq '.' 2>/dev/null || curl -s "$API_BASE/licenses"
echo -e "\n"

# 6. Test Machines
echo -e "${GREEN}6. Machines${NC}"
echo "List Machines:"
curl -s "$API_BASE/machines" | jq '.' 2>/dev/null || curl -s "$API_BASE/machines"
echo -e "\n"

# 7. Test Users
echo -e "${GREEN}7. Users${NC}"
echo "List Users:"
curl -s "$API_BASE/users" | jq '.' 2>/dev/null || curl -s "$API_BASE/users"
echo -e "\n"

echo "======================================"
echo "Individual tests completed!"
echo "Use the full test_api.sh script for comprehensive testing."
